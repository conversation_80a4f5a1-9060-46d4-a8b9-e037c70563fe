using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.AspNetCore.Mvc.Rendering;
using Volo.Abp.Application.Dtos;
using Volo.Abp.AspNetCore.Mvc.UI.RazorPages;
using Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Form;
using BookStoreHtmx.Authors;
using BookStoreHtmx.Shared;

namespace BookStoreHtmx.Web.Pages.Authors
{
    public class IndexModel : AbpPageModel
    {
        public string? FullNameFilter { get; set; }
        public DateOnly? BirthDateFilterMin { get; set; }

        public DateOnly? BirthDateFilterMax { get; set; }

        protected IAuthorsAppService _authorsAppService;

        public IndexModel(IAuthorsAppService authorsAppService)
        {
            _authorsAppService = authorsAppService;
        }

        public virtual async Task OnGetAsync()
        {

            await Task.CompletedTask;
        }
    }
}