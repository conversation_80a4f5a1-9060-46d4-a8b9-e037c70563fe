using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.AspNetCore.Mvc.Rendering;
using Volo.Abp.Application.Dtos;
using Volo.Abp.AspNetCore.Mvc.UI.RazorPages;
using Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Form;
using BookStoreHtmx.Authors;
using BookStoreHtmx.Shared;
using BookStoreHtmx.Pages;
using BookStoreHtmx.Extensions;

namespace BookStoreHtmx.Web.Pages.Authors
{
    public class IndexModel : HtmxPageModelBase
    {
        public string? FullNameFilter { get; set; }
        public DateOnly? BirthDateFilterMin { get; set; }
        public DateOnly? BirthDateFilterMax { get; set; }

        protected IAuthorsAppService _authorsAppService;

        public IndexModel(IAuthorsAppService authorsAppService)
        {
            _authorsAppService = authorsAppService;
        }

        public virtual async Task OnGetAsync()
        {
            await Task.CompletedTask;
        }

        public virtual async Task<IActionResult> OnGetTableDataAsync(
            string? filterText = null,
            string? fullName = null,
            DateOnly? birthDateMin = null,
            DateOnly? birthDateMax = null,
            int pageIndex = 1,
            int pageSize = 10)
        {
            var input = new GetAuthorsInput
            {
                FilterText = filterText,
                FullName = fullName,
                BirthDateMin = birthDateMin,
                BirthDateMax = birthDateMax,
                MaxResultCount = pageSize,
                SkipCount = (pageIndex - 1) * pageSize,
                Sorting = "fullName"
            };

            var result = await _authorsAppService.GetListAsync(input);

            var response = new HtmxTableResponse<AuthorDto>
            {
                Data = result.Items,
                TotalCount = (int)result.TotalCount,
                PageIndex = pageIndex,
                PageSize = pageSize
            };

            return Partial("_TableRows", response);
        }
    }
}