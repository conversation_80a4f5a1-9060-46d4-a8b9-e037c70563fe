@using Microsoft.AspNetCore.Authorization
@using BookStoreHtmx.Permissions
@using BookStoreHtmx.Localization
@using Microsoft.AspNetCore.Mvc.Localization
@inject IHtmlLocalizer<BookStoreHtmxResource> L
@inject IAuthorizationService Authorization
@model BookStoreHtmx.Extensions.HtmxTableResponse<BookStoreHtmx.Books.BookWithNavigationPropertiesDto>

@foreach (var item in Model.Data)
{
    <tr>
        @if (await Authorization.IsGrantedAsync(BookStoreHtmxPermissions.Books.Delete))
        {
            <td>
                <input type="checkbox" class="form-check-input select-row-checkbox" data-id="@item.Book.Id" />
            </td>
        }
        <td>
            <div class="dropdown">
                <button class="btn btn-primary btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                    @L["Actions"]
                </button>
                <ul class="dropdown-menu">
                    @if (await Authorization.IsGrantedAsync(BookStoreHtmxPermissions.Books.Edit))
                    {
                        <li>
                            <button class="dropdown-item" 
                                    htmx-get="/Books/EditModal?id=@item.Book.Id"
                                    htmx-target="#htmx-modal-content"
                                    data-bs-toggle="modal" 
                                    data-bs-target="#htmx-modal">
                                @L["Edit"]
                            </button>
                        </li>
                    }
                    @if (await Authorization.IsGrantedAsync(BookStoreHtmxPermissions.Books.Delete))
                    {
                        <li>
                            <button class="dropdown-item text-danger" 
                                    htmx-delete="/api/app/books/@item.Book.Id"
                                    htmx-confirm="@L["DeleteConfirmationMessage"]"
                                    htmx-target="closest tr"
                                    htmx-swap="delete">
                                @L["Delete"]
                            </button>
                        </li>
                    }
                </ul>
            </div>
        </td>
        <td>@item.Book.Name</td>
        <td>@item.Book.PublishDate.ToString("yyyy-MM-dd")</td>
        <td>@item.Book.Price.ToString("C")</td>
        <td>@(item.Author?.FullName ?? "")</td>
    </tr>
}

@if (!Model.Data.Any())
{
    <tr>
        <td colspan="6" class="text-center text-muted">
            @L["NoDataAvailable"]
        </td>
    </tr>
}

@* Pagination *@
@if (Model.TotalCount > Model.PageSize)
{
    <tr>
        <td colspan="6">
            <nav aria-label="Table pagination">
                <ul class="pagination justify-content-center mb-0">
                    @if (Model.HasPreviousPage)
                    {
                        <li class="page-item">
                            <button class="page-link" 
                                    htmx-get="/Books?pageIndex=@(Model.PageIndex - 1)&pageSize=@Model.PageSize"
                                    htmx-target="#BooksTable tbody"
                                    htmx-include="#SearchForm">
                                @L["Previous"]
                            </button>
                        </li>
                    }
                    
                    @for (int i = Math.Max(1, Model.PageIndex - 2); i <= Math.Min(Model.PageIndex + 2, (int)Math.Ceiling((double)Model.TotalCount / Model.PageSize)); i++)
                    {
                        <li class="page-item @(i == Model.PageIndex ? "active" : "")">
                            <button class="page-link" 
                                    htmx-get="/Books?pageIndex=@i&pageSize=@Model.PageSize"
                                    htmx-target="#BooksTable tbody"
                                    htmx-include="#SearchForm">
                                @i
                            </button>
                        </li>
                    }
                    
                    @if (Model.HasNextPage)
                    {
                        <li class="page-item">
                            <button class="page-link" 
                                    htmx-get="/Books?pageIndex=@(Model.PageIndex + 1)&pageSize=@Model.PageSize"
                                    htmx-target="#BooksTable tbody"
                                    htmx-include="#SearchForm">
                                @L["Next"]
                            </button>
                        </li>
                    }
                </ul>
            </nav>
        </td>
    </tr>
}
