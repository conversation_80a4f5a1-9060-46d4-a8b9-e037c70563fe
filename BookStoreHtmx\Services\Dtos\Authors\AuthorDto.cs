using System;
using System.Collections.Generic;

using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Entities;

namespace BookStoreHtmx.Authors
{
    public class AuthorDto : FullAuditedEntityDto<Guid>, IHasConcurrencyStamp
    {
        public string FullName { get; set; } = null!;
        public DateOnly BirthDate { get; set; }
        public string? Bio { get; set; }

        public string ConcurrencyStamp { get; set; } = null!;

    }
}