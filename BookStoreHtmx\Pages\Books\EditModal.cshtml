@page
@using Microsoft.AspNetCore.Mvc.Localization
@using BookStoreHtmx.Localization
@using BookStoreHtmx.Web.Pages.Books
@using Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Modal
@using BookStoreHtmx.Books;
@using System.Globalization
@inject IHtmlLocalizer<BookStoreHtmxResource> L
@model EditModalModel
@{
    Layout = null;
}

<form htmx-post="/Books/EditModal"
      htmx-target="#htmx-modal-content"
      htmx-swap="outerHTML"
      autocomplete="off"
      class="htmx-modal-form">
    <abp-modal id="BookEditModal">
        <abp-modal-header title="@L["Update"].Value"></abp-modal-header>

        <abp-modal-body>


            <abp-input asp-for="Id" />

                
                    
                    <abp-input asp-for="Book.ConcurrencyStamp" hidden="true" suppress-label="true"/>
                    

            <abp-input asp-for="Book.Name"    />


            <abp-input asp-for="Book.PublishDate"    type="date"/>


            <abp-input asp-for="Book.Price"   data-val="false" />


            <abp-input asp-for="Book.Description" text-area   />

                                <label for="Author_FullName">@L["Author"] *</label>
            <div class="input-group mb-3">
                <input hidden value="@(Model.Book.AuthorId == default ? "" : Model.Book.AuthorId.ToString())"
                       id="Author_Id" name="Book.AuthorId"/>
                <input type="text" id="Author_FullName" class="form-control" value="@Model.Author?.FullName" disabled>
                <button type="button" class="btn btn-info text-light"
                        htmx-get="/Shared/LookupModal?serviceMethod=getAuthorLookup&currentId=@(Model.Book.AuthorId)&currentDisplayName=@(Model.Author?.FullName ?? "")"
                        htmx-target="#htmx-modal-content"
                        data-bs-toggle="modal"
                        data-bs-target="#htmx-modal">@L["Pick"]</button>
                <button type="button" class="btn btn-danger ms-1" onclick="clearAuthorSelection()">
                    <i class="fa fa-times"></i>
                </button>
            </div>

                


        </abp-modal-body>

        <abp-modal-footer>
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">@L["Cancel"]</button>
            <button type="submit" class="btn btn-primary">
                <i class="fa fa-check"></i> @L["Save"]
            </button>
        </abp-modal-footer>
    </abp-modal>
</form>

<script>
    function selectAuthor(authorId, authorName) {
        document.getElementById('Author_Id').value = authorId;
        document.getElementById('Author_FullName').value = authorName;
    }

    function clearAuthorSelection() {
        document.getElementById('Author_Id').value = '';
        document.getElementById('Author_FullName').value = '';
    }
</script>
