@page
@using Microsoft.AspNetCore.Mvc.Localization
@using BookStoreHtmx.Localization
@using BookStoreHtmx.Web.Pages.Books
@using Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Modal
@using BookStoreHtmx.Books;
@using System.Globalization
@inject IHtmlLocalizer<BookStoreHtmxResource> L
@model EditModalModel
@{
    Layout = null;
}

<form data-ajaxForm="true" asp-page="/Books/EditModal" autocomplete="off">
    <abp-modal id="BookEditModal">
        <abp-modal-header title="@L["Update"].Value"></abp-modal-header>

        <abp-modal-body>


            <abp-input asp-for="Id" />

                
                    
                    <abp-input asp-for="Book.ConcurrencyStamp" hidden="true" suppress-label="true"/>
                    

            <abp-input asp-for="Book.Name"    />


            <abp-input asp-for="Book.PublishDate"    type="date"/>


            <abp-input asp-for="Book.Price"   data-val="false" />


            <abp-input asp-for="Book.Description" text-area   />

                                <label for="Author_FullName">@L["Author"] *</label>
            <div class="input-group mb-3">
                <input hidden value="@(Model.Book.AuthorId   == default ? "": Model.Book.AuthorId.ToString()
)" id="Author_Id" name="Book.AuthorId"/>
                <input type="text" id="Author_FullName" class="form-control" value="@Model.Author?.FullName" disabled>
                <abp-button button-type="Info" id="AuthorLookupOpenButton" class="text-light">@L["Pick"]</abp-button>
                <abp-button button-type="Danger" class="lookupCleanButton ms-1"><i class="fa fa-times"></i></abp-button>
            </div>

                


        </abp-modal-body>

        <abp-modal-footer buttons="@(AbpModalButtons.Cancel|AbpModalButtons.Save)">

        </abp-modal-footer>
    </abp-modal>
</form>
