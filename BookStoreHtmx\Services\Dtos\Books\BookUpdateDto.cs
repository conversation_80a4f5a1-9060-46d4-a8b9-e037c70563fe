using System;
using System.ComponentModel.DataAnnotations;
using System.Collections.Generic;
using Volo.Abp.Domain.Entities;

namespace BookStoreHtmx.Books
{
    public class BookUpdateDto : IHasConcurrencyStamp
    {
        [Required]
        public string Name { get; set; } = null!;
        public DateOnly PublishDate { get; set; }
        public float Price { get; set; }
        public string? Description { get; set; }
        public Guid AuthorId { get; set; }

        public string ConcurrencyStamp { get; set; } = null!;
    }
}