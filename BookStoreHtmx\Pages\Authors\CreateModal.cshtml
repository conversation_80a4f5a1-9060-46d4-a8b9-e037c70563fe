@page
@using Microsoft.AspNetCore.Mvc.Localization
@using BookStoreHtmx.Localization
@using Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Modal
@using BookStoreHtmx.Web.Pages.Authors
@using BookStoreHtmx.Authors;
@using System.Globalization
@inject IHtmlLocalizer<BookStoreHtmxResource> L
@model CreateModalModel
@{
    Layout = null;
}


<form data-ajaxForm="true" asp-page="/Authors/CreateModal" autocomplete="off">
    <abp-modal id="AuthorCreateModal">
        <abp-modal-header title="@L["NewAuthor"].Value"></abp-modal-header>

        <abp-modal-body>

                
                    
                    

            <abp-input asp-for="Author.FullName"    />


            <abp-input asp-for="Author.BirthDate"    type="date" value="@DateTime.Now"/>


            <abp-input asp-for="Author.Bio" text-area   />

                    
                


        </abp-modal-body>

        <abp-modal-footer buttons="@(AbpModalButtons.Cancel|AbpModalButtons.Save)">

        </abp-modal-footer>
    </abp-modal>
</form>
