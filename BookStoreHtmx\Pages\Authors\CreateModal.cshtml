@page
@using Microsoft.AspNetCore.Mvc.Localization
@using BookStoreHtmx.Localization
@using Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Modal
@using BookStoreHtmx.Web.Pages.Authors
@using BookStoreHtmx.Authors;
@using System.Globalization
@inject IHtmlLocalizer<BookStoreHtmxResource> L
@model CreateModalModel
@{
    Layout = null;
}


<form htmx-post="/Authors/CreateModal"
      htmx-target="#htmx-modal-content"
      htmx-swap="outerHTML"
      autocomplete="off"
      class="htmx-modal-form">
    <abp-modal id="AuthorCreateModal">
        <abp-modal-header title="@L["NewAuthor"].Value"></abp-modal-header>

        <abp-modal-body>

                
                    
                    

            <abp-input asp-for="Author.FullName"    />


            <abp-input asp-for="Author.BirthDate"    type="date" value="@DateTime.Now"/>


            <abp-input asp-for="Author.Bio" text-area   />

                    
                


        </abp-modal-body>

        <abp-modal-footer>
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">@L["Cancel"]</button>
            <button type="submit" class="btn btn-primary">
                <i class="fa fa-check"></i> @L["Save"]
            </button>
        </abp-modal-footer>
    </abp-modal>
</form>
