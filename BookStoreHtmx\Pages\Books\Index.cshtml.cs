using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.AspNetCore.Mvc.Rendering;
using Volo.Abp.Application.Dtos;
using Volo.Abp.AspNetCore.Mvc.UI.RazorPages;
using Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Form;
using BookStoreHtmx.Books;
using BookStoreHtmx.Shared;
using BookStoreHtmx.Pages;
using BookStoreHtmx.Extensions;

namespace BookStoreHtmx.Web.Pages.Books
{
    public class IndexModel : HtmxPageModelBase
    {
        public string? NameFilter { get; set; }
        public DateOnly? PublishDateFilterMin { get; set; }
        public DateOnly? PublishDateFilterMax { get; set; }
        public float? PriceFilterMin { get; set; }
        public float? PriceFilterMax { get; set; }
        public Guid? AuthorId { get; set; }

        protected IBooksAppService _booksAppService;

        public IndexModel(IBooksAppService booksAppService)
        {
            _booksAppService = booksAppService;
        }

        public virtual async Task OnGetAsync()
        {
            await Task.CompletedTask;
        }

        public virtual async Task<IActionResult> OnGetTableDataAsync(
            string? filterText = null,
            string? name = null,
            DateOnly? publishDateMin = null,
            DateOnly? publishDateMax = null,
            float? priceMin = null,
            float? priceMax = null,
            Guid? authorId = null,
            int pageIndex = 1,
            int pageSize = 10)
        {
            var input = new GetBooksInput
            {
                FilterText = filterText,
                Name = name,
                PublishDateMin = publishDateMin,
                PublishDateMax = publishDateMax,
                PriceMin = priceMin,
                PriceMax = priceMax,
                AuthorId = authorId,
                MaxResultCount = pageSize,
                SkipCount = (pageIndex - 1) * pageSize,
                Sorting = "book.name"
            };

            var result = await _booksAppService.GetListAsync(input);

            var response = new HtmxTableResponse<BookWithNavigationPropertiesDto>
            {
                Data = result.Items,
                TotalCount = (int)result.TotalCount,
                PageIndex = pageIndex,
                PageSize = pageSize
            };

            return Partial("_TableRows", response);
        }
    }
}