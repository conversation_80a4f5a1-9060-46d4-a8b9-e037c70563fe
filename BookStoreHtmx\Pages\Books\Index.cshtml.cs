using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.AspNetCore.Mvc.Rendering;
using Volo.Abp.Application.Dtos;
using Volo.Abp.AspNetCore.Mvc.UI.RazorPages;
using Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Form;
using BookStoreHtmx.Books;
using BookStoreHtmx.Shared;

namespace BookStoreHtmx.Web.Pages.Books
{
    public class IndexModel : AbpPageModel
    {
        public string? NameFilter { get; set; }
        public DateOnly? PublishDateFilterMin { get; set; }

        public DateOnly? PublishDateFilterMax { get; set; }
        public float? PriceFilterMin { get; set; }

        public float? PriceFilterMax { get; set; }

        protected IBooksAppService _booksAppService;

        public IndexModel(IBooksAppService booksAppService)
        {
            _booksAppService = booksAppService;
        }

        public virtual async Task OnGetAsync()
        {

            await Task.CompletedTask;
        }
    }
}