{
  "id": "96a69c55-76a1-45b8-a316-bbd00211f8e9",
  "template": "app-nolayers",
  "versions": {
    "LeptonX": "4.2.1",
    "AbpFramework": "9.2.1",
    "AbpCommercial": "9.2.1",
    "AbpStudio": "1.0.2",
    "TargetDotnetFramework": "net9.0"
  },
  "modules": {
    "BookStoreHtmx": {
      "path": "BookStoreHtmx.abpmdl"
    }
  },
  "runProfiles": {
    "Default": {
      "path": "etc/run-profiles/Default.abprun.json"
    }
  },
  "options": {
    "httpRequests": {
      "ignoredUrls": [
      
      ]
    }
  },
  "creatingStudioConfiguration": {
    "template": "app-nolayers",
    "createdAbpStudioVersion": "1.0.2",
    "multiTenancy": "false",
    "runInstallLibs": "true",
    "useLocalReferences": "false",
    "uiFramework": "mvc",
    "databaseProvider": "ef",
    "runDbMigrator": "true",
    "databaseManagementSystem": "sqlserver",
    "createInitialMigration": "true",
    "theme": "leptonx",
    "themeStyle": "dim",
    "themeMenuPlacement": "side",
    "publicWebsite": "",
    "optionalModules": " TextTemplateManagement LanguageManagement AuditLogging OpenIddictAdmin",
    "socialLogin": "true",
    "selectedLanguages": ["العربية", "English", ],
    "defaultLanguage": "العربية",
    "createCommand": "abp new BookStoreHtmx -t app-nolayers --ui-framework mvc --mobile  --database-provider ef --database-management-system sqlserver --theme leptonx --no-tests --without-cms-kit --dont-run-bundling --no-multi-tenancy -no-saas -no-gdpr -no-file-management"
  }
}