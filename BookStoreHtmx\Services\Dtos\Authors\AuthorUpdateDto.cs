using System;
using System.ComponentModel.DataAnnotations;
using System.Collections.Generic;
using Volo.Abp.Domain.Entities;

namespace BookStoreHtmx.Authors
{
    public class AuthorUpdateDto : IHasConcurrencyStamp
    {
        [Required]
        public string FullName { get; set; } = null!;
        public DateOnly BirthDate { get; set; }
        public string? Bio { get; set; }

        public string ConcurrencyStamp { get; set; } = null!;
    }
}