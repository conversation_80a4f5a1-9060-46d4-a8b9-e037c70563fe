using Volo.Abp.AspNetCore.Mvc.UI.RazorPages;
using BookStoreHtmx.Shared;
using BookStoreHtmx.Authors;
using System;
using System.Linq;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Volo.Abp.Application.Dtos;
using BookStoreHtmx.Books;
using BookStoreHtmx.Pages;
using BookStoreHtmx.Extensions;

namespace BookStoreHtmx.Web.Pages.Books
{
    public class EditModalModel : HtmxPageModelBase
    {
        [HiddenInput]
        [BindProperty(SupportsGet = true)]
        public Guid Id { get; set; }

        [BindProperty]
        public BookUpdateViewModel Book { get; set; }

        public AuthorDto Author { get; set; }

        protected IBooksAppService _booksAppService;

        public EditModalModel(IBooksAppService booksAppService)
        {
            _booksAppService = booksAppService;

            Book = new();
        }

        public virtual async Task OnGetAsync()
        {
            var bookWithNavigationPropertiesDto = await _booksAppService.GetWithNavigationPropertiesAsync(Id);
            Book = ObjectMapper.Map<BookDto, BookUpdateViewModel>(bookWithNavigationPropertiesDto.Book);

            Author = bookWithNavigationPropertiesDto.Author;

        }

        public virtual async Task<IActionResult> OnPostAsync()
        {
            return await ValidateAndProcessAsync(async () =>
            {
                await _booksAppService.UpdateAsync(Id, ObjectMapper.Map<BookUpdateViewModel, BookUpdateDto>(Book));

                return HandleFormResult(
                    isSuccess: true,
                    successMessage: L["SuccessfullyUpdated"],
                    redirectUrl: "./Index"
                );
            }, L["AnErrorOccurred"]);
        }
    }

    public class BookUpdateViewModel : BookUpdateDto
    {
    }
}