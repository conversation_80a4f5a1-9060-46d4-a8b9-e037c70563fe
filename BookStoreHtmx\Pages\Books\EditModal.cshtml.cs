using Volo.Abp.AspNetCore.Mvc.UI.RazorPages;
using BookStoreHtmx.Shared;
using BookStoreHtmx.Authors;
using System;
using System.Linq;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Volo.Abp.Application.Dtos;
using BookStoreHtmx.Books;

namespace BookStoreHtmx.Web.Pages.Books
{
    public class EditModalModel : AbpPageModel
    {
        [HiddenInput]
        [BindProperty(SupportsGet = true)]
        public Guid Id { get; set; }

        [BindProperty]
        public BookUpdateViewModel Book { get; set; }

        public AuthorDto Author { get; set; }

        protected IBooksAppService _booksAppService;

        public EditModalModel(IBooksAppService booksAppService)
        {
            _booksAppService = booksAppService;

            Book = new();
        }

        public virtual async Task OnGetAsync()
        {
            var bookWithNavigationPropertiesDto = await _booksAppService.GetWithNavigationPropertiesAsync(Id);
            Book = ObjectMapper.Map<BookDto, BookUpdateViewModel>(bookWithNavigationPropertiesDto.Book);

            Author = bookWithNavigationPropertiesDto.Author;

        }

        public virtual async Task<NoContentResult> OnPostAsync()
        {

            await _booksAppService.UpdateAsync(Id, ObjectMapper.Map<BookUpdateViewModel, BookUpdateDto>(Book));
            return NoContent();
        }
    }

    public class BookUpdateViewModel : BookUpdateDto
    {
    }
}