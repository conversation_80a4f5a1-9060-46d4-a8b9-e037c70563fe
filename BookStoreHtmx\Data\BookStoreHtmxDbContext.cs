using BookStoreHtmx.Books;
using BookStoreHtmx.Authors;
using Microsoft.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore.Modeling;
using Volo.Abp.AuditLogging.EntityFrameworkCore;
using Volo.Abp.BackgroundJobs.EntityFrameworkCore;
using Volo.Abp.BlobStoring.Database.EntityFrameworkCore;
using Volo.Abp.FeatureManagement.EntityFrameworkCore;
using Volo.Abp.Identity.EntityFrameworkCore;
using Volo.Abp.OpenIddict.EntityFrameworkCore;
using Volo.Abp.PermissionManagement.EntityFrameworkCore;
using Volo.Abp.SettingManagement.EntityFrameworkCore;
using Volo.Abp.TextTemplateManagement.EntityFrameworkCore;
using Volo.Abp.LanguageManagement.EntityFrameworkCore;

namespace BookStoreHtmx.Data;

public class BookStoreHtmxDbContext : AbpDbContext<BookStoreHtmxDbContext>
{
    public DbSet<Book> Books { get; set; } = null!;
    public DbSet<Author> Authors { get; set; } = null!;

    public const string DbTablePrefix = "App";
    public const string DbSchema = null;

    public BookStoreHtmxDbContext(DbContextOptions<BookStoreHtmxDbContext> options)
        : base(options)
    {
    }

    protected override void OnModelCreating(ModelBuilder builder)
    {
        base.OnModelCreating(builder);

        /* Include modules to your migration db context */

        builder.ConfigureSettingManagement();
        builder.ConfigureBackgroundJobs();
        builder.ConfigureAuditLogging();
        builder.ConfigureFeatureManagement();
        builder.ConfigurePermissionManagement();
        builder.ConfigureBlobStoring();
        builder.ConfigureIdentityPro();
        builder.ConfigureOpenIddictPro();
        builder.ConfigureLanguageManagement();
        builder.ConfigureTextTemplateManagement();

        /* Configure your own entities here */
        if (builder.IsHostDatabase())
        {

        }
        if (builder.IsHostDatabase())
        {

        }

        if (builder.IsHostDatabase())
        {
            builder.Entity<Author>(b =>
            {
                b.ToTable(DbTablePrefix + "Authors", DbSchema);
                b.ConfigureByConvention();
                b.Property(x => x.FullName).HasColumnName(nameof(Author.FullName)).IsRequired();
                b.Property(x => x.BirthDate).HasColumnName(nameof(Author.BirthDate));
                b.Property(x => x.Bio).HasColumnName(nameof(Author.Bio));
            });

        }
        if (builder.IsHostDatabase())
        {
            builder.Entity<Book>(b =>
            {
                b.ToTable(DbTablePrefix + "Books", DbSchema);
                b.ConfigureByConvention();
                b.Property(x => x.Name).HasColumnName(nameof(Book.Name)).IsRequired();
                b.Property(x => x.PublishDate).HasColumnName(nameof(Book.PublishDate));
                b.Property(x => x.Price).HasColumnName(nameof(Book.Price));
                b.Property(x => x.Description).HasColumnName(nameof(Book.Description));
                b.HasOne<Author>().WithMany().IsRequired().HasForeignKey(x => x.AuthorId).OnDelete(DeleteBehavior.NoAction);
            });

        }
    }
}