using Volo.Abp.AspNetCore.Mvc.UI.RazorPages;
using BookStoreHtmx.Shared;
using System;
using System.Collections.Generic;
using Microsoft.AspNetCore.Mvc.Rendering;
using Volo.Abp.Application.Dtos;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using BookStoreHtmx.Books;
using BookStoreHtmx.Pages;
using BookStoreHtmx.Extensions;

namespace BookStoreHtmx.Web.Pages.Books
{
    public class CreateModalModel : HtmxPageModelBase
    {

        [BindProperty]
        public BookCreateViewModel Book { get; set; }

        protected IBooksAppService _booksAppService;

        public CreateModalModel(IBooksAppService booksAppService)
        {
            _booksAppService = booksAppService;

            Book = new();
        }

        public virtual async Task OnGetAsync()
        {
            Book = new BookCreateViewModel();

            await Task.CompletedTask;
        }

        public virtual async Task<IActionResult> OnPostAsync()
        {
            return await ValidateAndProcessAsync(async () =>
            {
                await _booksAppService.CreateAsync(ObjectMapper.Map<BookCreateViewModel, BookCreateDto>(Book));

                return HandleFormResult(
                    isSuccess: true,
                    successMessage: L["SuccessfullyCreated"],
                    redirectUrl: "./Index"
                );
            }, L["AnErrorOccurred"]);
        }
    }

    public class BookCreateViewModel : BookCreateDto
    {
    }
}