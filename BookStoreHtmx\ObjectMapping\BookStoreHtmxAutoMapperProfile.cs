using BookStoreHtmx.Web.Pages.Books;
using BookStoreHtmx.Books;
using BookStoreHtmx.Web.Pages.Authors;
using BookStoreHtmx.Authors;
using System;
using BookStoreHtmx.Shared;
using Volo.Abp.AutoMapper;
using AutoMapper;

namespace BookStoreHtmx.ObjectMapping;

public class BookStoreHtmxAutoMapperProfile : Profile
{
    public BookStoreHtmxAutoMapperProfile()
    {
        /* Create your AutoMapper object mappings here */

        CreateMap<Author, AuthorDto>();

        CreateMap<AuthorDto, AuthorUpdateViewModel>();
        CreateMap<AuthorUpdateViewModel, AuthorUpdateDto>();
        CreateMap<AuthorCreateViewModel, AuthorCreateDto>();

        CreateMap<Book, BookDto>();
        CreateMap<Book, BookExcelDto>();
        CreateMap<BookWithNavigationProperties, BookWithNavigationPropertiesDto>();
        CreateMap<Author, LookupDto<Guid>>().ForMember(dest => dest.DisplayName, opt => opt.MapFrom(src => src.FullName));

        CreateMap<BookDto, BookUpdateViewModel>();
        CreateMap<BookUpdateViewModel, BookUpdateDto>();
        CreateMap<BookCreateViewModel, BookCreateDto>();
    }
}