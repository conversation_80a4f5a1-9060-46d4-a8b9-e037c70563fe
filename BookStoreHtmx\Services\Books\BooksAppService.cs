using BookStoreHtmx.Shared;
using BookStoreHtmx.Authors;
using System;
using System.IO;
using System.Linq;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq.Dynamic.Core;
using Microsoft.AspNetCore.Authorization;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;
using BookStoreHtmx.Permissions;
using BookStoreHtmx.Books;
using MiniExcelLibs;
using Volo.Abp.Content;
using Volo.Abp.Authorization;
using Volo.Abp.Caching;
using Microsoft.Extensions.Caching.Distributed;
using BookStoreHtmx.Shared;

namespace BookStoreHtmx.Books
{

    [Authorize(BookStoreHtmxPermissions.Books.Default)]
    public class BooksAppService : ApplicationService, IBooksAppService
    {
        protected IDistributedCache<BookDownloadTokenCacheItem, string> _downloadTokenCache;
        protected IBookRepository _bookRepository;
        protected BookManager _bookManager;

        protected IRepository<BookStoreHtmx.Authors.Author, Guid> _authorRepository;

        public BooksAppService(IBookRepository bookRepository, BookManager bookManager, IDistributedCache<BookDownloadTokenCacheItem, string> downloadTokenCache, IRepository<BookStoreHtmx.Authors.Author, Guid> authorRepository)
        {
            _downloadTokenCache = downloadTokenCache;
            _bookRepository = bookRepository;
            _bookManager = bookManager; _authorRepository = authorRepository;

        }

        public virtual async Task<PagedResultDto<BookWithNavigationPropertiesDto>> GetListAsync(GetBooksInput input)
        {
            var totalCount = await _bookRepository.GetCountAsync(input.FilterText, input.Name, input.PublishDateMin, input.PublishDateMax, input.PriceMin, input.PriceMax, input.AuthorId);
            var items = await _bookRepository.GetListWithNavigationPropertiesAsync(input.FilterText, input.Name, input.PublishDateMin, input.PublishDateMax, input.PriceMin, input.PriceMax, input.AuthorId, input.Sorting, input.MaxResultCount, input.SkipCount);

            return new PagedResultDto<BookWithNavigationPropertiesDto>
            {
                TotalCount = totalCount,
                Items = ObjectMapper.Map<List<BookWithNavigationProperties>, List<BookWithNavigationPropertiesDto>>(items)
            };
        }

        public virtual async Task<BookWithNavigationPropertiesDto> GetWithNavigationPropertiesAsync(Guid id)
        {
            return ObjectMapper.Map<BookWithNavigationProperties, BookWithNavigationPropertiesDto>
                (await _bookRepository.GetWithNavigationPropertiesAsync(id));
        }

        public virtual async Task<BookDto> GetAsync(Guid id)
        {
            return ObjectMapper.Map<Book, BookDto>(await _bookRepository.GetAsync(id));
        }

        public virtual async Task<PagedResultDto<LookupDto<Guid>>> GetAuthorLookupAsync(LookupRequestDto input)
        {
            var query = (await _authorRepository.GetQueryableAsync())
                .WhereIf(!string.IsNullOrWhiteSpace(input.Filter),
                    x => x.FullName != null &&
                         x.FullName.Contains(input.Filter));

            var lookupData = await query.PageBy(input.SkipCount, input.MaxResultCount).ToDynamicListAsync<BookStoreHtmx.Authors.Author>();
            var totalCount = query.Count();
            return new PagedResultDto<LookupDto<Guid>>
            {
                TotalCount = totalCount,
                Items = ObjectMapper.Map<List<BookStoreHtmx.Authors.Author>, List<LookupDto<Guid>>>(lookupData)
            };
        }

        [Authorize(BookStoreHtmxPermissions.Books.Delete)]
        public virtual async Task DeleteAsync(Guid id)
        {
            await _bookRepository.DeleteAsync(id);
        }

        [Authorize(BookStoreHtmxPermissions.Books.Create)]
        public virtual async Task<BookDto> CreateAsync(BookCreateDto input)
        {
            if (input.AuthorId == default)
            {
                throw new UserFriendlyException(L["The {0} field is required.", L["Author"]]);
            }

            var book = await _bookManager.CreateAsync(
            input.AuthorId, input.Name, input.PublishDate, input.Price, input.Description
            );

            return ObjectMapper.Map<Book, BookDto>(book);
        }

        [Authorize(BookStoreHtmxPermissions.Books.Edit)]
        public virtual async Task<BookDto> UpdateAsync(Guid id, BookUpdateDto input)
        {
            if (input.AuthorId == default)
            {
                throw new UserFriendlyException(L["The {0} field is required.", L["Author"]]);
            }

            var book = await _bookManager.UpdateAsync(
            id,
            input.AuthorId, input.Name, input.PublishDate, input.Price, input.Description, input.ConcurrencyStamp
            );

            return ObjectMapper.Map<Book, BookDto>(book);
        }

        [AllowAnonymous]
        public virtual async Task<IRemoteStreamContent> GetListAsExcelFileAsync(BookExcelDownloadDto input)
        {
            var downloadToken = await _downloadTokenCache.GetAsync(input.DownloadToken);
            if (downloadToken == null || input.DownloadToken != downloadToken.Token)
            {
                throw new AbpAuthorizationException("Invalid download token: " + input.DownloadToken);
            }

            var books = await _bookRepository.GetListWithNavigationPropertiesAsync(input.FilterText, input.Name, input.PublishDateMin, input.PublishDateMax, input.PriceMin, input.PriceMax, input.AuthorId);
            var items = books.Select(item => new
            {
                Name = item.Book.Name,
                PublishDate = item.Book.PublishDate,
                Price = item.Book.Price,
                Description = item.Book.Description,

                Author = item.Author?.FullName,

            });

            var memoryStream = new MemoryStream();
            await memoryStream.SaveAsAsync(items);
            memoryStream.Seek(0, SeekOrigin.Begin);

            return new RemoteStreamContent(memoryStream, "Books.xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        }

        [Authorize(BookStoreHtmxPermissions.Books.Delete)]
        public virtual async Task DeleteByIdsAsync(List<Guid> bookIds)
        {
            await _bookRepository.DeleteManyAsync(bookIds);
        }

        [Authorize(BookStoreHtmxPermissions.Books.Delete)]
        public virtual async Task DeleteAllAsync(GetBooksInput input)
        {
            await _bookRepository.DeleteAllAsync(input.FilterText, input.Name, input.PublishDateMin, input.PublishDateMax, input.PriceMin, input.PriceMax, input.AuthorId);
        }
        public virtual async Task<BookStoreHtmx.Shared.DownloadTokenResultDto> GetDownloadTokenAsync()
        {
            var token = Guid.NewGuid().ToString("N");

            await _downloadTokenCache.SetAsync(
                token,
                new BookDownloadTokenCacheItem { Token = token },
                new DistributedCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = TimeSpan.FromSeconds(30)
                });

            return new BookStoreHtmx.Shared.DownloadTokenResultDto
            {
                Token = token
            };
        }
    }
}