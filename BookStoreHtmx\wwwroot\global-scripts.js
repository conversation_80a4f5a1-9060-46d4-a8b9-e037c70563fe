/* Your Global Scripts */

// HTMX Configuration and ABP Integration
(function() {
    'use strict';

    // Configure HTMX globally
    if (typeof htmx !== 'undefined') {
        // Configure HTMX to work with ABP's CSRF tokens
        htmx.config.requestClass = 'htmx-request';
        htmx.config.indicatorClass = 'htmx-indicator';
        htmx.config.historyCacheSize = 0; // Disable history cache for better compatibility with ABP

        // Add CSRF token to all HTMX requests
        document.body.addEventListener('htmx:configRequest', function(evt) {
            var token = document.querySelector('input[name="__RequestVerificationToken"]');
            if (token) {
                evt.detail.headers['RequestVerificationToken'] = token.value;
            }
        });

        // Handle ABP's standard error responses
        document.body.addEventListener('htmx:responseError', function(evt) {
            if (evt.detail.xhr.status === 400) {
                try {
                    var response = JSON.parse(evt.detail.xhr.responseText);
                    if (response.error && response.error.message) {
                        abp.notify.error(response.error.message);
                    }
                } catch (e) {
                    abp.notify.error('An error occurred while processing your request.');
                }
            }
        });

        // Handle successful responses with ABP notifications
        document.body.addEventListener('htmx:afterRequest', function(evt) {
            if (evt.detail.xhr.status === 200) {
                var response = evt.detail.xhr.responseText;
                try {
                    var jsonResponse = JSON.parse(response);
                    if (jsonResponse.success && jsonResponse.message) {
                        abp.notify.success(jsonResponse.message);
                    }
                } catch (e) {
                    // Not JSON response, continue normally
                }
            }
        });

        // Handle HTMX trigger events for ABP integration
        document.body.addEventListener('htmx:trigger', function(evt) {
            if (evt.detail.showNotification) {
                var notification = evt.detail.showNotification;
                if (notification.type === 'success') {
                    abp.notify.success(notification.message);
                } else if (notification.type === 'error') {
                    abp.notify.error(notification.message);
                } else if (notification.type === 'info') {
                    abp.notify.info(notification.message);
                }
            }

            if (evt.detail.closeModal) {
                // Close Bootstrap modal
                var modal = document.querySelector('.modal.show');
                if (modal) {
                    var bsModal = bootstrap.Modal.getInstance(modal);
                    if (bsModal) {
                        bsModal.hide();
                    }
                }
            }

            if (evt.detail.refreshTarget) {
                var target = document.querySelector(evt.detail.refreshTarget);
                if (target && target.hasAttribute('hx-get')) {
                    htmx.trigger(target, 'refresh');
                }
            }
        });
    }

    // ABP-HTMX Integration Utilities
    window.abpHtmx = {
        // Reload a specific HTMX element
        reload: function(selector) {
            var element = document.querySelector(selector);
            if (element && element.hasAttribute('hx-get')) {
                htmx.trigger(element, 'refresh');
            }
        },

        // Show loading indicator
        showLoading: function(element) {
            if (element) {
                element.classList.add('htmx-request');
            }
        },

        // Hide loading indicator
        hideLoading: function(element) {
            if (element) {
                element.classList.remove('htmx-request');
            }
        },

        // Confirm action with ABP's message system
        confirm: function(message, callback) {
            abp.message.confirm(message).then(function(confirmed) {
                if (confirmed && callback) {
                    callback();
                }
            });
        }
    };
})();
