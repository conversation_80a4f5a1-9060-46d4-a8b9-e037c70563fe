var abp = abp || {};



abp.modals.bookCreate = function () {
    var initModal = function (publicApi, args) {
        var l = abp.localization.getResource("BookStoreHtmx");
        
        
        
        var lastNpIdId = '';
        var lastNpDisplayNameId = '';

        var _lookupModal = new abp.ModalManager({
            viewUrl: abp.appPath + "Shared/LookupModal",
            scriptUrl: abp.appPath + "Pages/Shared/lookupModal.js",
            modalClass: "navigationPropertyLookup"
        });

        $('.lookupCleanButton').on('click', '', function () {
            $(this).parent().find('input').val('');
        });

        _lookupModal.onClose(function () {
            var modal = $(_lookupModal.getModal());
            $('#' + lastNpIdId).val(modal.find('#CurrentLookupId').val());
            $('#' + lastNpDisplayNameId).val(modal.find('#CurrentLookupDisplayName').val());
        });
        
        $('#AuthorLookupOpenButton').on('click', '', function () {
            lastNpDisplayNameId = 'Author_FullName';
            lastNpIdId = 'Author_Id';
            _lookupModal.open({
                currentId: $('#Author_Id').val(),
                currentDisplayName: $('#Author_FullName').val(),
                serviceMethod: function() {
                    
                    return window.bookStoreHtmx.books.books.getAuthorLookup;
                }
            });
        });
        
        
    };
    
    

    return {
        initModal: initModal
    };
};

