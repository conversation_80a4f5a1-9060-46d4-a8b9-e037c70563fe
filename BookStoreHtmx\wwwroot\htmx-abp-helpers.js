/**
 * HTMX-ABP Integration Helpers
 * Provides utilities to integrate HTMX with ABP Framework patterns
 */
(function() {
    'use strict';
    
    // Namespace for HTMX-ABP helpers
    window.HtmxAbp = {
        
        /**
         * Modal Management for HTMX
         */
        Modal: {
            /**
             * Open a modal using HTMX
             * @param {string} url - The URL to load modal content from
             * @param {string} target - The target selector for the modal
             * @param {object} options - Additional options
             */
            open: function(url, target, options) {
                options = options || {};
                var targetElement = document.querySelector(target || '#htmx-modal-container');
                
                if (!targetElement) {
                    // Create modal container if it doesn't exist
                    targetElement = document.createElement('div');
                    targetElement.id = 'htmx-modal-container';
                    document.body.appendChild(targetElement);
                }
                
                // Use HTMX to load modal content
                htmx.ajax('GET', url, {
                    target: targetElement,
                    swap: 'innerHTML'
                });
            },
            
            /**
             * Close the current modal
             */
            close: function() {
                var modalContainer = document.querySelector('#htmx-modal-container');
                if (modalContainer) {
                    modalContainer.innerHTML = '';
                }
                
                // Remove Bootstrap modal backdrop if exists
                var backdrop = document.querySelector('.modal-backdrop');
                if (backdrop) {
                    backdrop.remove();
                }
                
                // Remove modal-open class from body
                document.body.classList.remove('modal-open');
            },
            
            /**
             * Handle modal form submission
             * @param {Event} event - The form submission event
             */
            handleFormSubmit: function(event) {
                event.preventDefault();
                var form = event.target;
                var formData = new FormData(form);
                
                htmx.ajax('POST', form.action, {
                    values: formData,
                    target: form.closest('.modal'),
                    swap: 'outerHTML'
                });
            }
        },
        
        /**
         * Table Management for HTMX
         */
        Table: {
            /**
             * Refresh a table
             * @param {string} tableSelector - The table selector
             */
            refresh: function(tableSelector) {
                var table = document.querySelector(tableSelector);
                if (table && table.hasAttribute('hx-get')) {
                    htmx.trigger(table, 'refresh');
                }
            },
            
            /**
             * Handle table row actions
             * @param {Event} event - The click event
             * @param {string} action - The action type (edit, delete, etc.)
             * @param {string} id - The record ID
             */
            handleRowAction: function(event, action, id) {
                event.preventDefault();
                
                if (action === 'delete') {
                    var l = abp.localization.getResource('BookStoreHtmx');
                    abp.message.confirm(l('DeleteConfirmationMessage')).then(function(confirmed) {
                        if (confirmed) {
                            // Perform delete action
                            var deleteUrl = event.target.getAttribute('data-delete-url') || 
                                           event.target.closest('[data-delete-url]').getAttribute('data-delete-url');
                            
                            htmx.ajax('DELETE', deleteUrl.replace('{id}', id), {
                                target: event.target.closest('tr'),
                                swap: 'delete'
                            });
                        }
                    });
                } else if (action === 'edit') {
                    var editUrl = event.target.getAttribute('data-edit-url') || 
                                 event.target.closest('[data-edit-url]').getAttribute('data-edit-url');
                    
                    HtmxAbp.Modal.open(editUrl.replace('{id}', id));
                }
            }
        },
        
        /**
         * Form Utilities
         */
        Form: {
            /**
             * Handle form validation errors
             * @param {object} errors - Validation errors object
             * @param {HTMLElement} form - The form element
             */
            displayValidationErrors: function(errors, form) {
                // Clear existing errors
                var existingErrors = form.querySelectorAll('.field-validation-error');
                existingErrors.forEach(function(error) {
                    error.textContent = '';
                    error.classList.remove('field-validation-error');
                });

                // Remove existing validation summary errors
                var validationSummary = form.querySelector('.validation-summary-errors');
                if (validationSummary) {
                    validationSummary.innerHTML = '';
                    validationSummary.classList.add('validation-summary-valid');
                    validationSummary.classList.remove('validation-summary-errors');
                }

                // Display new errors
                if (errors) {
                    for (var field in errors) {
                        var errorElement = form.querySelector('[data-valmsg-for="' + field + '"]');
                        if (errorElement && errors[field].length > 0) {
                            errorElement.textContent = errors[field][0];
                            errorElement.classList.add('field-validation-error');
                            errorElement.classList.remove('field-validation-valid');
                        }
                    }
                }
            },

            /**
             * Reset form validation
             * @param {HTMLElement} form - The form element
             */
            resetValidation: function(form) {
                var errors = form.querySelectorAll('.field-validation-error');
                errors.forEach(function(error) {
                    error.textContent = '';
                    error.classList.remove('field-validation-error');
                    error.classList.add('field-validation-valid');
                });

                var validationSummary = form.querySelector('.validation-summary-errors');
                if (validationSummary) {
                    validationSummary.innerHTML = '';
                    validationSummary.classList.add('validation-summary-valid');
                    validationSummary.classList.remove('validation-summary-errors');
                }
            },

            /**
             * Validate form using ABP's client-side validation
             * @param {HTMLElement} form - The form element
             * @returns {boolean} - True if form is valid
             */
            validate: function(form) {
                if (typeof $ !== 'undefined' && $.validator) {
                    var validator = $(form).data('validator');
                    if (validator) {
                        return validator.form();
                    }
                }
                return form.checkValidity();
            },

            /**
             * Handle form submission with validation
             * @param {Event} event - The form submission event
             */
            handleSubmit: function(event) {
                var form = event.target;

                // Reset validation state
                HtmxAbp.Form.resetValidation(form);

                // Validate form
                if (!HtmxAbp.Form.validate(form)) {
                    event.preventDefault();
                    return false;
                }

                return true;
            }
        },
        
        /**
         * Search and Filter utilities
         */
        Search: {
            /**
             * Handle search form submission
             * @param {Event} event - The form submission event
             * @param {string} targetTable - The target table selector
             */
            handleSubmit: function(event, targetTable) {
                event.preventDefault();
                var form = event.target;
                var formData = new FormData(form);
                var params = new URLSearchParams(formData);

                var table = document.querySelector(targetTable);
                if (table) {
                    var baseUrl = table.getAttribute('hx-get').split('?')[0];
                    var newUrl = baseUrl + '?' + params.toString();

                    table.setAttribute('hx-get', newUrl);
                    htmx.trigger(table, 'refresh');
                }
            },

            /**
             * Clear all filters in a form
             * @param {string} formSelector - The form selector
             * @param {string} targetTable - The target table selector
             */
            clearFilters: function(formSelector, targetTable) {
                var form = document.querySelector(formSelector);
                if (form) {
                    // Clear all input fields
                    var inputs = form.querySelectorAll('input[type="text"], input[type="date"], input[type="number"], input[type="email"], select');
                    inputs.forEach(function(input) {
                        if (input.type === 'checkbox' || input.type === 'radio') {
                            input.checked = false;
                        } else {
                            input.value = '';
                        }
                    });

                    // Trigger form submission to refresh results
                    this.handleSubmit({ target: form, preventDefault: function() {} }, targetTable);
                }
            },

            /**
             * Set up debounced search for input fields
             * @param {string} inputSelector - The input field selector
             * @param {string} formSelector - The form selector
             * @param {number} delay - Delay in milliseconds (default: 500)
             */
            setupDebouncedSearch: function(inputSelector, formSelector, delay) {
                delay = delay || 500;
                var input = document.querySelector(inputSelector);
                var form = document.querySelector(formSelector);

                if (input && form) {
                    var timeoutId;
                    input.addEventListener('input', function() {
                        clearTimeout(timeoutId);
                        timeoutId = setTimeout(function() {
                            htmx.trigger(form, 'submit');
                        }, delay);
                    });
                }
            },

            /**
             * Get current filter values from form
             * @param {string} formSelector - The form selector
             * @returns {object} - Object containing filter values
             */
            getFilterValues: function(formSelector) {
                var form = document.querySelector(formSelector);
                if (form) {
                    var formData = new FormData(form);
                    var filters = {};
                    for (var pair of formData.entries()) {
                        if (pair[1]) { // Only include non-empty values
                            filters[pair[0]] = pair[1];
                        }
                    }
                    return filters;
                }
                return {};
            }
        },
        
        /**
         * Notification helpers
         */
        Notify: {
            /**
             * Show success notification
             * @param {string} message - The message to display
             */
            success: function(message) {
                if (typeof abp !== 'undefined' && abp.notify) {
                    abp.notify.success(message);
                }
            },
            
            /**
             * Show error notification
             * @param {string} message - The message to display
             */
            error: function(message) {
                if (typeof abp !== 'undefined' && abp.notify) {
                    abp.notify.error(message);
                }
            },
            
            /**
             * Show info notification
             * @param {string} message - The message to display
             */
            info: function(message) {
                if (typeof abp !== 'undefined' && abp.notify) {
                    abp.notify.info(message);
                }
            }
        }
    };
    
    // Global event handlers for HTMX-ABP integration
    document.addEventListener('DOMContentLoaded', function() {
        // Handle modal close buttons
        document.addEventListener('click', function(event) {
            if (event.target.matches('[data-htmx-modal-close]')) {
                HtmxAbp.Modal.close();
            }
        });

        // Handle form submissions in modals
        document.addEventListener('submit', function(event) {
            if (event.target.matches('.htmx-modal-form')) {
                return HtmxAbp.Form.handleSubmit(event);
            }
        });

        // Handle HTMX form validation errors
        document.addEventListener('htmx:responseError', function(event) {
            var form = event.target.closest('form');
            if (form && event.detail.xhr.status === 400) {
                try {
                    var response = JSON.parse(event.detail.xhr.responseText);
                    if (response.errors) {
                        HtmxAbp.Form.displayValidationErrors(response.errors, form);
                    }
                } catch (e) {
                    console.error('Error parsing validation response:', e);
                }
            }
        });

        // Handle successful form submissions
        document.addEventListener('htmx:afterRequest', function(event) {
            var form = event.target.closest('form');
            if (form && event.detail.xhr.status === 200) {
                // Check if this was a successful form submission
                var contentType = event.detail.xhr.getResponseHeader('content-type');
                if (contentType && contentType.includes('application/json')) {
                    try {
                        var response = JSON.parse(event.detail.xhr.responseText);
                        if (response.success) {
                            // Form was successfully submitted
                            HtmxAbp.Form.resetValidation(form);
                        }
                    } catch (e) {
                        // Not JSON response, continue normally
                    }
                }
            }
        });
    });
    
})();
