using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using JetBrains.Annotations;
using Volo.Abp;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Domain.Services;
using Volo.Abp.Data;

namespace BookStoreHtmx.Authors
{
    public class AuthorManager : DomainService
    {
        protected IAuthorRepository _authorRepository;

        public AuthorManager(IAuthorRepository authorRepository)
        {
            _authorRepository = authorRepository;
        }

        public virtual async Task<Author> CreateAsync(
        string fullName, DateOnly birthDate, string? bio = null)
        {
            Check.NotNullOrWhiteSpace(fullName, nameof(fullName));

            var author = new Author(
             GuidGenerator.Create(),
             fullName, birthDate, bio
             );

            return await _authorRepository.InsertAsync(author);
        }

        public virtual async Task<Author> UpdateAsync(
            Guid id,
            string fullName, DateOnly birthDate, string? bio = null, [CanBeNull] string? concurrencyStamp = null
        )
        {
            Check.NotNullOrWhiteSpace(fullName, nameof(fullName));

            var author = await _authorRepository.GetAsync(id);

            author.FullName = fullName;
            author.BirthDate = birthDate;
            author.Bio = bio;

            author.SetConcurrencyStampIfNotNull(concurrencyStamp);
            return await _authorRepository.UpdateAsync(author);
        }

    }
}