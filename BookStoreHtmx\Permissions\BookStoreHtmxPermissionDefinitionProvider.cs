using BookStoreHtmx.Localization;
using Volo.Abp.Authorization.Permissions;
using Volo.Abp.Localization;
using Volo.Abp.MultiTenancy;

namespace BookStoreHtmx.Permissions;

public class BookStoreHtmxPermissionDefinitionProvider : PermissionDefinitionProvider
{
    public override void Define(IPermissionDefinitionContext context)
    {
        var myGroup = context.AddGroup(BookStoreHtmxPermissions.GroupName);

        myGroup.AddPermission(BookStoreHtmxPermissions.Dashboard.Host, L("Permission:Dashboard"), MultiTenancySides.Host);

        //Define your own permissions here. Example:
        //myGroup.AddPermission(BookStoreHtmxPermissions.MyPermission1, L("Permission:MyPermission1"));

        var authorPermission = myGroup.AddPermission(BookStoreHtmxPermissions.Authors.Default, L("Permission:Authors"));
        authorPermission.AddChild(BookStoreHtmxPermissions.Authors.Create, L("Permission:Create"));
        authorPermission.AddChild(BookStoreHtmxPermissions.Authors.Edit, L("Permission:Edit"));
        authorPermission.AddChild(BookStoreHtmxPermissions.Authors.Delete, L("Permission:Delete"));

        var bookPermission = myGroup.AddPermission(BookStoreHtmxPermissions.Books.Default, L("Permission:Books"));
        bookPermission.AddChild(BookStoreHtmxPermissions.Books.Create, L("Permission:Create"));
        bookPermission.AddChild(BookStoreHtmxPermissions.Books.Edit, L("Permission:Edit"));
        bookPermission.AddChild(BookStoreHtmxPermissions.Books.Delete, L("Permission:Delete"));
    }

    private static LocalizableString L(string name)
    {
        return LocalizableString.Create<BookStoreHtmxResource>(name);
    }
}