{"Culture": "en", "Texts": {"AppName": "BookStoreHtmx", "Welcome_Title": "Welcome", "Welcome_Text": "This is a minimalist, single layer application startup template for the ABP Framework.", "Permission:Dashboard": "Dashboard", "Menu:Dashboard": "Dashboard", "Dashboard": "Dashboard", "ExternalProvider:Google": "Google", "ExternalProvider:Google:ClientId": "Client ID", "ExternalProvider:Google:ClientSecret": "Client secret", "ExternalProvider:Microsoft": "Microsoft", "ExternalProvider:Microsoft:ClientId": "Client ID", "ExternalProvider:Microsoft:ClientSecret": "Client secret", "ExternalProvider:Twitter": "Twitter", "ExternalProvider:Twitter:ConsumerKey": "Consumer key", "ExternalProvider:Twitter:ConsumerSecret": "Consumer secret", "Menu:Home": "Home", "Permission:Authors": "Authors", "Permission:BookStoreHtmx": "Book Store Htmx", "Permission:Create": "Create", "Permission:Edit": "Edit", "Permission:Delete": "Delete", "Authors": "Authors", "NewAuthor": "New Author", "Actions": "Actions", "SuccessfullyDeleted": "Successfully deleted", "DeleteConfirmationMessage": "Are you sure you want to delete this record?", "Search": "Search", "Pick": "Pick", "SeeAdvancedFilters": "Filters", "ItemAlreadyAdded": "This item is already added.", "ExportToExcel": "Export to Excel", "AllItemsAreSelected": "All {0} items are selected", "OneItemOnThisPageIsSelected": "1 item on this page is selected", "NumberOfItemsOnThisPageAreSelected": "All {0} items on this page are selected", "SelectAllItems": "Select all {0} items", "ClearSelection": "Clear selection", "DeleteAllRecords": "Are you sure you want to delete all records?", "DeleteSelectedRecords": "Are you sure you want to delete {0} record(s)?", "UploadFailedMessage": "Upload Failed: Unsupported file format or file size too large. Please ensure the file meets the required format and size limits, and try again.", "DownloadSelectedFile": "Download selected file", "RemoveSelectedFile": "Remove selected file", "FileUploading": "File uploading...", "MaxFileSizeLimit": "Max file size: {0}mb", "Filters": "Filters", "FullName": "Full Name", "BirthDate": "Birth Date", "Bio": "Bio", "MinBirthDate": "Min Birth Date", "MaxBirthDate": "Max Birth Date", "Menu:Authors": "Authors", "Menu:BookStoreHtmx": "Book Store Htmx", "Books": "Books", "NewBook": "New Book", "Name": "Name", "PublishDate": "Publish Date", "Price": "Price", "MinPublishDate": "Min Publish Date", "MinPrice": "<PERSON>", "MaxPublishDate": "Max Publish Date", "MaxPrice": "Max Price", "Permission:Books": "Books", "Author": "Author", "Description": "Description", "Menu:Books": "Books"}}