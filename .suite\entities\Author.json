{"Id": "3cb026d2-15eb-4268-bd1f-72e0a5dad3ee", "Name": "Author", "OriginalName": "Author", "NamePlural": "Authors", "DatabaseTableName": "Authors", "Namespace": "Authors", "Type": 1, "MasterEntityName": null, "MasterEntity": null, "BaseClass": "FullAuditedAggregateRoot", "PageTitle": "Authors", "MenuIcon": "file-alt", "PrimaryKeyType": "Guid", "PreserveCustomCode": false, "IsMultiTenant": false, "CheckConcurrency": true, "BulkDeleteEnabled": false, "ShouldCreateUserInterface": true, "ShouldCreateBackend": true, "ShouldExportExcel": false, "ShouldAddMigration": true, "ShouldUpdateDatabase": true, "CreateTests": true, "Properties": [{"Id": "2f86a29d-f2b4-4964-88f6-0caf843cbc58", "Name": "FullName", "Type": "string", "EnumType": "", "EnumNamespace": "", "EnumAngularImport": "shared/enums", "EnumFilePath": null, "DefaultValue": null, "IsNullable": false, "IsRequired": true, "IsFilterable": true, "AllowEmptyStrings": false, "IsTextArea": false, "MinLength": null, "MaxLength": null, "SortOrder": 0, "SortType": 0, "Regex": "", "EmailValidation": false, "ShowOnList": true, "ShowOnCreateModal": true, "ShowOnEditModal": true, "ReadonlyOnEditModal": false, "EnumValues": null, "IsSelected": true, "MaxFileSize": null, "OrdinalIndex": 0}, {"Id": "01569806-17fa-4dc8-a430-6b40e6ec7ffc", "Name": "BirthDate", "Type": "DateOnly", "EnumType": "", "EnumNamespace": "", "EnumAngularImport": "shared/enums", "EnumFilePath": null, "DefaultValue": null, "IsNullable": false, "IsRequired": false, "IsFilterable": true, "AllowEmptyStrings": false, "IsTextArea": false, "MinLength": null, "MaxLength": null, "SortOrder": 0, "SortType": 0, "Regex": "", "EmailValidation": false, "ShowOnList": true, "ShowOnCreateModal": true, "ShowOnEditModal": true, "ReadonlyOnEditModal": false, "EnumValues": null, "IsSelected": true, "MaxFileSize": null, "OrdinalIndex": 0}, {"Id": "33328cfe-3ab9-42b3-bd8a-2427f7ad0154", "Name": "Bio", "Type": "string", "EnumType": "", "EnumNamespace": "", "EnumAngularImport": "shared/enums", "EnumFilePath": null, "DefaultValue": null, "IsNullable": false, "IsRequired": false, "IsFilterable": false, "AllowEmptyStrings": false, "IsTextArea": true, "MinLength": null, "MaxLength": null, "SortOrder": 0, "SortType": 0, "Regex": "", "EmailValidation": false, "ShowOnList": false, "ShowOnCreateModal": true, "ShowOnEditModal": true, "ReadonlyOnEditModal": false, "EnumValues": null, "IsSelected": true, "MaxFileSize": null, "OrdinalIndex": 0}], "NavigationProperties": [], "NavigationConnections": [], "ChildEntities": [], "PhysicalFileName": "Author.json"}