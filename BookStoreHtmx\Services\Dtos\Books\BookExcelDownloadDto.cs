using Volo.Abp.Application.Dtos;
using System;

namespace BookStoreHtmx.Books
{
    public class BookExcelDownloadDto
    {
        public string DownloadToken { get; set; } = null!;

        public string? FilterText { get; set; }

        public string? Name { get; set; }
        public DateOnly? PublishDateMin { get; set; }
        public DateOnly? PublishDateMax { get; set; }
        public float? PriceMin { get; set; }
        public float? PriceMax { get; set; }
        public Guid? AuthorId { get; set; }

        public BookExcelDownloadDto()
        {

        }
    }
}