{"Id": "c488cc3b-d9b8-486e-85c5-a95aad53cb6d", "Name": "Book", "OriginalName": "Book", "NamePlural": "Books", "DatabaseTableName": "Books", "Namespace": "Books", "Type": 1, "MasterEntityName": null, "MasterEntity": null, "BaseClass": "FullAuditedAggregateRoot", "PageTitle": "Books", "MenuIcon": "file-alt", "PrimaryKeyType": "Guid", "PreserveCustomCode": false, "IsMultiTenant": false, "CheckConcurrency": true, "BulkDeleteEnabled": true, "ShouldCreateUserInterface": true, "ShouldCreateBackend": true, "ShouldExportExcel": true, "ShouldAddMigration": true, "ShouldUpdateDatabase": true, "CreateTests": true, "Properties": [{"Id": "e4ad0dcb-dfd5-4edb-88c5-633441042a5a", "Name": "Name", "Type": "string", "EnumType": "", "EnumNamespace": "", "EnumAngularImport": "shared/enums", "EnumFilePath": null, "DefaultValue": null, "IsNullable": false, "IsRequired": true, "IsFilterable": true, "AllowEmptyStrings": false, "IsTextArea": false, "MinLength": null, "MaxLength": null, "SortOrder": 0, "SortType": 0, "Regex": "", "EmailValidation": false, "ShowOnList": true, "ShowOnCreateModal": true, "ShowOnEditModal": true, "ReadonlyOnEditModal": false, "EnumValues": null, "IsSelected": true, "MaxFileSize": null, "OrdinalIndex": 0}, {"Id": "96fa6461-4098-4cd0-be56-07e70aa1b47b", "Name": "PublishDate", "Type": "DateOnly", "EnumType": "", "EnumNamespace": "", "EnumAngularImport": "shared/enums", "EnumFilePath": null, "DefaultValue": null, "IsNullable": false, "IsRequired": false, "IsFilterable": true, "AllowEmptyStrings": false, "IsTextArea": false, "MinLength": null, "MaxLength": null, "SortOrder": 0, "SortType": 0, "Regex": "", "EmailValidation": false, "ShowOnList": true, "ShowOnCreateModal": true, "ShowOnEditModal": true, "ReadonlyOnEditModal": false, "EnumValues": null, "IsSelected": true, "MaxFileSize": null, "OrdinalIndex": 0}, {"Id": "50a89a5d-927a-4c62-b041-1fdc253730d7", "Name": "Price", "Type": "float", "EnumType": "", "EnumNamespace": "", "EnumAngularImport": "shared/enums", "EnumFilePath": null, "DefaultValue": null, "IsNullable": false, "IsRequired": false, "IsFilterable": true, "AllowEmptyStrings": false, "IsTextArea": false, "MinLength": null, "MaxLength": null, "SortOrder": 0, "SortType": 0, "Regex": "", "EmailValidation": false, "ShowOnList": true, "ShowOnCreateModal": true, "ShowOnEditModal": true, "ReadonlyOnEditModal": false, "EnumValues": null, "IsSelected": true, "MaxFileSize": null, "OrdinalIndex": 0}, {"Id": "708d027b-4f31-40c2-ab0b-4e1964907079", "Name": "Description", "Type": "string", "EnumType": "", "EnumNamespace": "", "EnumAngularImport": "shared/enums", "EnumFilePath": null, "DefaultValue": null, "IsNullable": false, "IsRequired": false, "IsFilterable": false, "AllowEmptyStrings": false, "IsTextArea": true, "MinLength": null, "MaxLength": null, "SortOrder": 0, "SortType": 0, "Regex": "", "EmailValidation": false, "ShowOnList": false, "ShowOnCreateModal": true, "ShowOnEditModal": true, "ReadonlyOnEditModal": false, "EnumValues": null, "IsSelected": true, "MaxFileSize": null, "OrdinalIndex": 0}], "NavigationProperties": [{"EntityNameWithDuplicationNumber": "Author", "EntitySetNameWithDuplicationNumber": "Authors", "ReferencePropertyName": "Author", "UiPickType": "Modal", "IsRequired": true, "IncludeEntitiesFromModules": false, "FromAbpModule": false, "Name": "AuthorId", "DisplayProperty": "FullName", "Namespace": "BookStoreHtmx.Authors", "EntityName": "Author", "EntitySetName": "Authors", "DtoNamespace": "BookStoreHtmx.Authors", "DtoEntityName": "AuthorDto", "Type": "Guid", "IsFilterable": true}], "NavigationConnections": [], "ChildEntities": [], "PhysicalFileName": "Book.json"}