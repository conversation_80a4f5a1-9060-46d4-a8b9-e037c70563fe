@using Microsoft.AspNetCore.Mvc.Localization
@using BookStoreHtmx.Localization
@inject IHtmlLocalizer<BookStoreHtmxResource> L
@model BookStoreHtmx.Extensions.HtmxTableResponse<BookStoreHtmx.Web.Pages.Shared.LookupItemDto>

@foreach (var item in Model.Data)
{
    <tr>
        <td>@item.DisplayName</td>
        <td>
            <button type="button" 
                    class="btn btn-primary btn-sm" 
                    onclick="selectLookupItem('@item.Id', '@Html.Raw(Html.Encode(item.DisplayName))')">
                <i class="fa fa-check"></i> @L["Pick"]
            </button>
        </td>
    </tr>
}

@if (!Model.Data.Any())
{
    <tr>
        <td colspan="2" class="text-center text-muted">
            @L["NoDataAvailable"]
        </td>
    </tr>
}

@* Pagination *@
@if (Model.TotalCount > Model.PageSize)
{
    <tr>
        <td colspan="2">
            <nav aria-label="Lookup pagination">
                <ul class="pagination justify-content-center mb-0 pagination-sm">
                    @if (Model.HasPreviousPage)
                    {
                        <li class="page-item">
                            <button class="page-link" 
                                    htmx-get="/Shared/LookupModal/TableData?pageIndex=@(Model.PageIndex - 1)&pageSize=@Model.PageSize"
                                    htmx-target="#LookupTable tbody"
                                    htmx-include="#LookupSearchForm">
                                @L["Previous"]
                            </button>
                        </li>
                    }
                    
                    @for (int i = Math.Max(1, Model.PageIndex - 2); i <= Math.Min(Model.PageIndex + 2, (int)Math.Ceiling((double)Model.TotalCount / Model.PageSize)); i++)
                    {
                        <li class="page-item @(i == Model.PageIndex ? "active" : "")">
                            <button class="page-link" 
                                    htmx-get="/Shared/LookupModal/TableData?pageIndex=@i&pageSize=@Model.PageSize"
                                    htmx-target="#LookupTable tbody"
                                    htmx-include="#LookupSearchForm">
                                @i
                            </button>
                        </li>
                    }
                    
                    @if (Model.HasNextPage)
                    {
                        <li class="page-item">
                            <button class="page-link" 
                                    htmx-get="/Shared/LookupModal/TableData?pageIndex=@(Model.PageIndex + 1)&pageSize=@Model.PageSize"
                                    htmx-target="#LookupTable tbody"
                                    htmx-include="#LookupSearchForm">
                                @L["Next"]
                            </button>
                        </li>
                    }
                </ul>
            </nav>
        </td>
    </tr>
}
