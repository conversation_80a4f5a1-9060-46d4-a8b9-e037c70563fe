@page
@using Microsoft.AspNetCore.Authorization
@using Volo.Abp.AspNetCore.Mvc.UI.Layout
@using BookStoreHtmx.Permissions
@using BookStoreHtmx.Web.Pages.Authors
@using BookStoreHtmx.Menus
@using Microsoft.AspNetCore.Mvc.Localization
@using BookStoreHtmx.Localization
@inject IHtmlLocalizer<BookStoreHtmxResource> L
@inject IAuthorizationService Authorization
@model IndexModel
@inject IPageLayout PageLayout
@{
    PageLayout.Content.Title = L["Authors"].Value;
    PageLayout.Content.MenuItemName = BookStoreHtmxMenus.Authors;
}

@section styles
{

}

@section scripts
{
    <abp-script src="/Pages/Authors/index.js" />

}

@section content_toolbar {

    @if (await Authorization.IsGrantedAsync(BookStoreHtmxPermissions.Authors.Create))
    {
        <abp-button id="NewAuthorButton" text="@L["NewAuthor"].Value" icon="plus" size="Small" button-type="Primary" />
    }

}



<abp-card>
    <abp-card-body>
		<abp-row class="mb-3">
            <abp-column size-md="_8" size-lg="_10">
                <div class="mb-3">
                    <form id="SearchForm" autocomplete="off">
                        <div class="input-group">
                            <input class="form-control page-search-filter-text" id="FilterText" placeholder="@L["Search"]"/>
                            <abp-button button-type="Primary" type="submit" icon="search"/>
                        </div>
                    </form>
                </div>
            </abp-column>
            <abp-column size-md="_4" size-lg="_2">
                <div class="mb-3">
                    <abp-button style="width:100%" id="AdvancedFilterSectionToggler" button-type="Outline_Primary">
                        @L["Filters"]<i aria-hidden="true" class="fa ms-1 fa-angle-down"></i>
                    </abp-button>
                </div>
            </abp-column>
        </abp-row>

        <abp-row id="AdvancedFilterSection" class="mt-3" style="display: none;">
            <abp-column size="_3">
                <abp-input asp-for="FullNameFilter" label="@L["FullName"].Value" />
            </abp-column>
            <abp-column size="_3">
                <abp-input type="date" asp-for="BirthDateFilterMin" label="@L["MinBirthDate"].Value" />
            </abp-column>

            <abp-column size="_3">
                <abp-input type="date" asp-for="BirthDateFilterMax" label="@L["MaxBirthDate"].Value" />
            </abp-column>

        </abp-row>


        <abp-table striped-rows="true" id="AuthorsTable">
            <thead>
				<tr>
				    
				    
					<th>@L["Actions"]</th>
					<th>@L["FullName"]</th>
					<th>@L["BirthDate"]</th>


				</tr>
            </thead>
        </abp-table>
    </abp-card-body>
</abp-card>

