@page
@using Microsoft.AspNetCore.Authorization
@using Volo.Abp.AspNetCore.Mvc.UI.Layout
@using BookStoreHtmx.Permissions
@using BookStoreHtmx.Web.Pages.Authors
@using BookStoreHtmx.Menus
@using Microsoft.AspNetCore.Mvc.Localization
@using BookStoreHtmx.Localization
@inject IHtmlLocalizer<BookStoreHtmxResource> L
@inject IAuthorizationService Authorization
@model IndexModel
@inject IPageLayout PageLayout
@{
    PageLayout.Content.Title = L["Authors"].Value;
    PageLayout.Content.MenuItemName = BookStoreHtmxMenus.Authors;
}

@section styles
{

}

@section scripts
{
    <script>
        // HTMX-based Authors page functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Handle advanced filter toggle
            document.getElementById('AdvancedFilterSectionToggler').addEventListener('click', function() {
                var section = document.getElementById('AdvancedFilterSection');
                var icon = this.querySelector('i');

                if (section.style.display === 'none') {
                    section.style.display = 'block';
                    icon.classList.remove('fa-angle-down');
                    icon.classList.add('fa-angle-up');
                } else {
                    section.style.display = 'none';
                    icon.classList.remove('fa-angle-up');
                    icon.classList.add('fa-angle-down');
                }
            });
        });

        // Filter management functions for Authors
        function clearAllAuthorsFilters() {
            // Clear all filter inputs
            document.getElementById('FilterText').value = '';
            document.getElementById('FullNameFilter').value = '';
            document.getElementById('BirthDateFilterMin').value = '';
            document.getElementById('BirthDateFilterMax').value = '';

            // Trigger search form submission to refresh results
            htmx.trigger(document.getElementById('SearchForm'), 'submit');
        }
    </script>
}

@section content_toolbar {
    @if (await Authorization.IsGrantedAsync(BookStoreHtmxPermissions.Authors.Create))
    {
        <button class="btn btn-primary btn-sm"
                htmx-get="/Authors/CreateModal"
                htmx-target="#htmx-modal-content"
                data-bs-toggle="modal"
                data-bs-target="#htmx-modal">
            <i class="fa fa-plus"></i> @L["NewAuthor"]
        </button>
    }
}



<abp-card>
    <abp-card-body>
		<abp-row class="mb-3">
            <abp-column size-md="_8" size-lg="_10">
                <div class="mb-3">
                    <form id="SearchForm" autocomplete="off"
                          htmx-get="/Authors/TableData"
                          htmx-target="#AuthorsTable tbody"
                          htmx-trigger="submit, input delay:500ms from:#FilterText">
                        <div class="input-group">
                            <input class="form-control page-search-filter-text" id="FilterText" name="filterText" placeholder="@L["Search"]"/>
                            <abp-button button-type="Primary" type="submit" icon="search"/>
                        </div>
                    </form>
                </div>
            </abp-column>
            <abp-column size-md="_4" size-lg="_2">
                <div class="mb-3">
                    <abp-button style="width:100%" id="AdvancedFilterSectionToggler" button-type="Outline_Primary">
                        @L["Filters"]<i aria-hidden="true" class="fa ms-1 fa-angle-down"></i>
                    </abp-button>
                </div>
            </abp-column>
        </abp-row>

        <div id="AdvancedFilterSection" class="mt-3" style="display: none;">
            <div class="row">
                <div class="col-md-4">
                    <abp-input asp-for="FullNameFilter" label="@L["FullName"].Value" />
                </div>
                <div class="col-md-3">
                    <abp-input type="date" asp-for="BirthDateFilterMin" label="@L["MinBirthDate"].Value" />
                </div>
                <div class="col-md-3">
                    <abp-input type="date" asp-for="BirthDateFilterMax" label="@L["MaxBirthDate"].Value" />
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button type="button" class="btn btn-secondary me-2" onclick="clearAllAuthorsFilters()">
                        <i class="fa fa-refresh"></i> @L["Clear"]
                    </button>
                </div>
            </div>
        </div>


        <div class="htmx-table-container">
            <table class="table table-striped" id="AuthorsTable">
                <thead>
                    <tr>
                        <th>@L["Actions"]</th>
                        <th>@L["FullName"]</th>
                        <th>@L["BirthDate"]</th>
                    </tr>
                </thead>
                <tbody htmx-get="/Authors/TableData"
                       htmx-trigger="load, refresh from:body"
                       htmx-include="#SearchForm"
                       htmx-indicator="#table-loading">
                    <!-- Table rows will be loaded via HTMX -->
                </tbody>
            </table>
            <div id="table-loading" class="htmx-indicator text-center p-3">
                <div class="spinner-border" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
            </div>
        </div>
    </abp-card-body>
</abp-card>

<!-- HTMX Modal Container -->
<div class="modal fade" id="htmx-modal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content" id="htmx-modal-content">
            <!-- Modal content will be loaded via HTMX -->
        </div>
    </div>
</div>

