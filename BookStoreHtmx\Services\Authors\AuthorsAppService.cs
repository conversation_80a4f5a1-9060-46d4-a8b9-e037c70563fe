using System;
using System.IO;
using System.Linq;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq.Dynamic.Core;
using Microsoft.AspNetCore.Authorization;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;
using BookStoreHtmx.Permissions;
using BookStoreHtmx.Authors;

namespace BookStoreHtmx.Authors
{

    [Authorize(BookStoreHtmxPermissions.Authors.Default)]
    public class AuthorsAppService : ApplicationService, IAuthorsAppService
    {

        protected IAuthorRepository _authorRepository;
        protected AuthorManager _authorManager;

        public AuthorsAppService(IAuthorRepository authorRepository, AuthorManager authorManager)
        {

            _authorRepository = authorRepository;
            _authorManager = authorManager;

        }

        public virtual async Task<PagedResultDto<AuthorDto>> GetListAsync(GetAuthorsInput input)
        {
            var totalCount = await _authorRepository.GetCountAsync(input.FilterText, input.FullName, input.BirthDateMin, input.BirthDateMax);
            var items = await _authorRepository.GetListAsync(input.FilterText, input.FullName, input.BirthDateMin, input.BirthDateMax, input.Sorting, input.MaxResultCount, input.SkipCount);

            return new PagedResultDto<AuthorDto>
            {
                TotalCount = totalCount,
                Items = ObjectMapper.Map<List<Author>, List<AuthorDto>>(items)
            };
        }

        public virtual async Task<AuthorDto> GetAsync(Guid id)
        {
            return ObjectMapper.Map<Author, AuthorDto>(await _authorRepository.GetAsync(id));
        }

        [Authorize(BookStoreHtmxPermissions.Authors.Delete)]
        public virtual async Task DeleteAsync(Guid id)
        {
            await _authorRepository.DeleteAsync(id);
        }

        [Authorize(BookStoreHtmxPermissions.Authors.Create)]
        public virtual async Task<AuthorDto> CreateAsync(AuthorCreateDto input)
        {

            var author = await _authorManager.CreateAsync(
            input.FullName, input.BirthDate, input.Bio
            );

            return ObjectMapper.Map<Author, AuthorDto>(author);
        }

        [Authorize(BookStoreHtmxPermissions.Authors.Edit)]
        public virtual async Task<AuthorDto> UpdateAsync(Guid id, AuthorUpdateDto input)
        {

            var author = await _authorManager.UpdateAsync(
            id,
            input.FullName, input.BirthDate, input.Bio, input.ConcurrencyStamp
            );

            return ObjectMapper.Map<Author, AuthorDto>(author);
        }
    }
}