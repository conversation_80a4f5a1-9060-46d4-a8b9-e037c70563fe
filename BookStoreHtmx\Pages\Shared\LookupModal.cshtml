@page
@using Microsoft.AspNetCore.Mvc.Localization
@using BookStoreHtmx.Localization
@inject IHtmlLocalizer<BookStoreHtmxResource> L
@model BookStoreHtmx.Web.Pages.Shared.LookupModalModel
@{
    Layout = null;
}

<div class="modal-header">
    <h5 class="modal-title">@L["SelectItem"]</h5>
    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
</div>

<div class="modal-body">
    <div class="mb-3">
        <form id="LookupSearchForm"
              htmx-get="/Shared/LookupModal/TableData"
              htmx-target="#LookupTable tbody"
              htmx-trigger="submit, input delay:300ms from:#LookupSearchText"
              htmx-include="[name='serviceMethod'], [name='currentId']">
            <input type="hidden" name="serviceMethod" value="@Model.ServiceMethod" />
            <input type="hidden" name="currentId" value="@Model.CurrentId" />
            <div class="input-group">
                <input type="text"
                       id="LookupSearchText"
                       name="filterText"
                       class="form-control"
                       placeholder="@L["Search"]"
                       value="@Model.FilterText" />
                <button type="submit" class="btn btn-primary">
                    <i class="fa fa-search"></i>
                </button>
            </div>
        </form>
    </div>

    <div class="table-responsive">
        <table class="table table-striped table-hover" id="LookupTable">
            <thead>
                <tr>
                    <th>@L["Name"]</th>
                    <th width="100">@L["Actions"]</th>
                </tr>
            </thead>
            <tbody htmx-get="/Shared/LookupModal/TableData?serviceMethod=@Model.ServiceMethod&currentId=@Model.CurrentId&filterText=@Model.FilterText"
                   htmx-trigger="load"
                   htmx-indicator="#lookup-loading">
                <!-- Table rows will be loaded via HTMX -->
            </tbody>
        </table>

        <div id="lookup-loading" class="htmx-indicator text-center p-3">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
        </div>
    </div>
</div>

<div class="modal-footer">
    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">@L["Cancel"]</button>
</div>

<script>
    // Handle lookup item selection
    function selectLookupItem(id, displayName) {
        // Call the parent window function to handle selection
        if (window.selectAuthor) {
            window.selectAuthor(id, displayName);
        } else {
            // Fallback: trigger custom event
            var event = new CustomEvent('lookupItemSelected', {
                detail: { id: id, displayName: displayName }
            });
            document.dispatchEvent(event);
        }

        // Close modal
        var modal = bootstrap.Modal.getInstance(document.querySelector('#htmx-modal'));
        if (modal) {
            modal.hide();
        }
    }
</script>


