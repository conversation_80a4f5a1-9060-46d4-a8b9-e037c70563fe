@page
@using Microsoft.AspNetCore.Mvc.Localization
@using BookStoreHtmx.Localization
@using BookStoreHtmx.Web.Pages.Authors
@using Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Modal
@using BookStoreHtmx.Authors;
@using System.Globalization
@inject IHtmlLocalizer<BookStoreHtmxResource> L
@model EditModalModel
@{
    Layout = null;
}

<form htmx-post="/Authors/EditModal"
      htmx-target="#htmx-modal-content"
      htmx-swap="outerHTML"
      autocomplete="off"
      class="htmx-modal-form">
    <abp-modal id="AuthorEditModal">
        <abp-modal-header title="@L["Update"].Value"></abp-modal-header>

        <abp-modal-body>


            <abp-input asp-for="Id" />

                
                    
                    <abp-input asp-for="Author.ConcurrencyStamp" hidden="true" suppress-label="true"/>
                    

            <abp-input asp-for="Author.FullName"    />


            <abp-input asp-for="Author.BirthDate"    type="date"/>


            <abp-input asp-for="Author.Bio" text-area   />

                    
                


        </abp-modal-body>

        <abp-modal-footer>
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">@L["Cancel"]</button>
            <button type="submit" class="btn btn-primary">
                <i class="fa fa-check"></i> @L["Save"]
            </button>
        </abp-modal-footer>
    </abp-modal>
</form>
