@page
@using Microsoft.AspNetCore.Mvc.Localization
@using BookStoreHtmx.Localization
@using BookStoreHtmx.Web.Pages.Authors
@using Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Modal
@using BookStoreHtmx.Authors;
@using System.Globalization
@inject IHtmlLocalizer<BookStoreHtmxResource> L
@model EditModalModel
@{
    Layout = null;
}

<form data-ajaxForm="true" asp-page="/Authors/EditModal" autocomplete="off">
    <abp-modal id="AuthorEditModal">
        <abp-modal-header title="@L["Update"].Value"></abp-modal-header>

        <abp-modal-body>


            <abp-input asp-for="Id" />

                
                    
                    <abp-input asp-for="Author.ConcurrencyStamp" hidden="true" suppress-label="true"/>
                    

            <abp-input asp-for="Author.FullName"    />


            <abp-input asp-for="Author.BirthDate"    type="date"/>


            <abp-input asp-for="Author.Bio" text-area   />

                    
                


        </abp-modal-body>

        <abp-modal-footer buttons="@(AbpModalButtons.Cancel|AbpModalButtons.Save)">

        </abp-modal-footer>
    </abp-modal>
</form>
