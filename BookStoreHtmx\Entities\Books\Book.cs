using BookStoreHtmx.Authors;
using System;
using System.Linq;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Entities.Auditing;
using Volo.Abp.MultiTenancy;
using JetBrains.Annotations;

using Volo.Abp;

namespace BookStoreHtmx.Books
{
    public class Book : FullAuditedAggregateRoot<Guid>
    {
        [NotNull]
        public virtual string Name { get; set; }

        public virtual DateOnly PublishDate { get; set; }

        public virtual float Price { get; set; }

        [CanBeNull]
        public virtual string? Description { get; set; }
        public Guid AuthorId { get; set; }

        protected Book()
        {

        }

        public Book(Guid id, Guid authorId, string name, DateOnly publishDate, float price, string? description = null)
        {

            Id = id;
            Check.NotNull(name, nameof(name));
            Name = name;
            PublishDate = publishDate;
            Price = price;
            Description = description;
            AuthorId = authorId;
        }

    }
}