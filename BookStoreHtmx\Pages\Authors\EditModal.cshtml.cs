using Volo.Abp.AspNetCore.Mvc.UI.RazorPages;
using BookStoreHtmx.Shared;
using System;
using System.Linq;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Volo.Abp.Application.Dtos;
using BookStoreHtmx.Authors;
using BookStoreHtmx.Pages;
using BookStoreHtmx.Extensions;

namespace BookStoreHtmx.Web.Pages.Authors
{
    public class EditModalModel : HtmxPageModelBase
    {
        [HiddenInput]
        [BindProperty(SupportsGet = true)]
        public Guid Id { get; set; }

        [BindProperty]
        public AuthorUpdateViewModel Author { get; set; }

        protected IAuthorsAppService _authorsAppService;

        public EditModalModel(IAuthorsAppService authorsAppService)
        {
            _authorsAppService = authorsAppService;

            Author = new();
        }

        public virtual async Task OnGetAsync()
        {
            var author = await _authorsAppService.GetAsync(Id);
            Author = ObjectMapper.Map<AuthorDto, AuthorUpdateViewModel>(author);

        }

        public virtual async Task<IActionResult> OnPostAsync()
        {
            return await ValidateAndProcessAsync(async () =>
            {
                await _authorsAppService.UpdateAsync(Id, ObjectMapper.Map<AuthorUpdateViewModel, AuthorUpdateDto>(Author));

                return HandleFormResult(
                    isSuccess: true,
                    successMessage: L["SuccessfullyUpdated"],
                    redirectUrl: "./Index"
                );
            }, L["AnErrorOccurred"]);
        }
    }

    public class AuthorUpdateViewModel : AuthorUpdateDto
    {
    }
}