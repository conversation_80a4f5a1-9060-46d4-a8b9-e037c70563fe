using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.AspNetCore.Mvc.ViewFeatures;
using Microsoft.AspNetCore.Razor.TagHelpers;
using System.Text.Encodings.Web;

namespace BookStoreHtmx.TagHelpers
{
    /// <summary>
    /// Tag helper for HTMX integration with ABP Framework
    /// </summary>
    [HtmlTargetElement("*", Attributes = "htmx-get")]
    [HtmlTargetElement("*", Attributes = "htmx-post")]
    [HtmlTargetElement("*", Attributes = "htmx-put")]
    [HtmlTargetElement("*", Attributes = "htmx-delete")]
    [HtmlTargetElement("*", Attributes = "htmx-patch")]
    public class HtmxTagHelper : TagHelper
    {
        [ViewContext]
        public ViewContext ViewContext { get; set; }

        [HtmlAttributeName("htmx-get")]
        public string HtmxGet { get; set; }

        [HtmlAttributeName("htmx-post")]
        public string HtmxPost { get; set; }

        [HtmlAttributeName("htmx-put")]
        public string HtmxPut { get; set; }

        [HtmlAttributeName("htmx-delete")]
        public string HtmxDelete { get; set; }

        [HtmlAttributeName("htmx-patch")]
        public string HtmxPatch { get; set; }

        [HtmlAttributeName("htmx-target")]
        public string HtmxTarget { get; set; }

        [HtmlAttributeName("htmx-swap")]
        public string HtmxSwap { get; set; }

        [HtmlAttributeName("htmx-trigger")]
        public string HtmxTrigger { get; set; }

        [HtmlAttributeName("htmx-confirm")]
        public string HtmxConfirm { get; set; }

        [HtmlAttributeName("htmx-indicator")]
        public string HtmxIndicator { get; set; }

        [HtmlAttributeName("htmx-include")]
        public string HtmxInclude { get; set; }

        [HtmlAttributeName("htmx-vals")]
        public string HtmxVals { get; set; }

        [HtmlAttributeName("htmx-headers")]
        public string HtmxHeaders { get; set; }

        [HtmlAttributeName("htmx-boost")]
        public bool? HtmxBoost { get; set; }

        [HtmlAttributeName("htmx-push-url")]
        public string HtmxPushUrl { get; set; }

        [HtmlAttributeName("htmx-select")]
        public string HtmxSelect { get; set; }

        [HtmlAttributeName("htmx-select-oob")]
        public string HtmxSelectOob { get; set; }

        [HtmlAttributeName("htmx-ext")]
        public string HtmxExt { get; set; }

        public override void Process(TagHelperContext context, TagHelperOutput output)
        {
            // Add CSRF token to headers for POST requests
            if (!string.IsNullOrEmpty(HtmxPost) || !string.IsNullOrEmpty(HtmxPut) || 
                !string.IsNullOrEmpty(HtmxDelete) || !string.IsNullOrEmpty(HtmxPatch))
            {
                var token = ViewContext.HttpContext.RequestServices
                    .GetService<Microsoft.AspNetCore.Antiforgery.IAntiforgery>()
                    ?.GetAndStoreTokens(ViewContext.HttpContext);

                if (token != null && !string.IsNullOrEmpty(HtmxHeaders))
                {
                    // Merge with existing headers
                    HtmxHeaders = $"{HtmxHeaders}, \"RequestVerificationToken\": \"{token.RequestToken}\"";
                }
                else if (token != null)
                {
                    HtmxHeaders = $"{{\"RequestVerificationToken\": \"{token.RequestToken}\"}}";
                }
            }

            // Set HTMX attributes
            SetAttributeIfNotNull(output, "hx-get", HtmxGet);
            SetAttributeIfNotNull(output, "hx-post", HtmxPost);
            SetAttributeIfNotNull(output, "hx-put", HtmxPut);
            SetAttributeIfNotNull(output, "hx-delete", HtmxDelete);
            SetAttributeIfNotNull(output, "hx-patch", HtmxPatch);
            SetAttributeIfNotNull(output, "hx-target", HtmxTarget);
            SetAttributeIfNotNull(output, "hx-swap", HtmxSwap);
            SetAttributeIfNotNull(output, "hx-trigger", HtmxTrigger);
            SetAttributeIfNotNull(output, "hx-confirm", HtmxConfirm);
            SetAttributeIfNotNull(output, "hx-indicator", HtmxIndicator);
            SetAttributeIfNotNull(output, "hx-include", HtmxInclude);
            SetAttributeIfNotNull(output, "hx-vals", HtmxVals);
            SetAttributeIfNotNull(output, "hx-headers", HtmxHeaders);
            SetAttributeIfNotNull(output, "hx-push-url", HtmxPushUrl);
            SetAttributeIfNotNull(output, "hx-select", HtmxSelect);
            SetAttributeIfNotNull(output, "hx-select-oob", HtmxSelectOob);
            SetAttributeIfNotNull(output, "hx-ext", HtmxExt);

            if (HtmxBoost.HasValue)
            {
                output.Attributes.SetAttribute("hx-boost", HtmxBoost.Value ? "true" : "false");
            }

            // Remove the custom attributes to avoid rendering them
            output.Attributes.RemoveAll("htmx-get");
            output.Attributes.RemoveAll("htmx-post");
            output.Attributes.RemoveAll("htmx-put");
            output.Attributes.RemoveAll("htmx-delete");
            output.Attributes.RemoveAll("htmx-patch");
            output.Attributes.RemoveAll("htmx-target");
            output.Attributes.RemoveAll("htmx-swap");
            output.Attributes.RemoveAll("htmx-trigger");
            output.Attributes.RemoveAll("htmx-confirm");
            output.Attributes.RemoveAll("htmx-indicator");
            output.Attributes.RemoveAll("htmx-include");
            output.Attributes.RemoveAll("htmx-vals");
            output.Attributes.RemoveAll("htmx-headers");
            output.Attributes.RemoveAll("htmx-boost");
            output.Attributes.RemoveAll("htmx-push-url");
            output.Attributes.RemoveAll("htmx-select");
            output.Attributes.RemoveAll("htmx-select-oob");
            output.Attributes.RemoveAll("htmx-ext");
        }

        private void SetAttributeIfNotNull(TagHelperOutput output, string attributeName, string value)
        {
            if (!string.IsNullOrEmpty(value))
            {
                output.Attributes.SetAttribute(attributeName, value);
            }
        }
    }

    /// <summary>
    /// Tag helper for HTMX table with ABP integration
    /// </summary>
    [HtmlTargetElement("htmx-table")]
    public class HtmxTableTagHelper : TagHelper
    {
        [HtmlAttributeName("data-url")]
        public string DataUrl { get; set; }

        [HtmlAttributeName("table-id")]
        public string TableId { get; set; } = "data-table";

        [HtmlAttributeName("auto-load")]
        public bool AutoLoad { get; set; } = true;

        [HtmlAttributeName("page-size")]
        public int PageSize { get; set; } = 10;

        public override void Process(TagHelperContext context, TagHelperOutput output)
        {
            output.TagName = "div";
            output.Attributes.SetAttribute("class", "htmx-table-container");
            output.Attributes.SetAttribute("id", $"{TableId}-container");

            var tableHtml = $@"
                <table id=""{TableId}"" class=""table table-striped"" 
                       hx-get=""{DataUrl}"" 
                       hx-trigger=""{(AutoLoad ? "load" : "refresh")}"" 
                       hx-target=""#{TableId} tbody""
                       hx-swap=""innerHTML""
                       hx-indicator=""#{TableId}-loading"">
                    <thead>
                        <!-- Table headers will be defined in the partial view -->
                    </thead>
                    <tbody>
                        <!-- Table rows will be loaded via HTMX -->
                    </tbody>
                </table>
                <div id=""{TableId}-loading"" class=""htmx-indicator text-center p-3"">
                    <div class=""spinner-border"" role=""status"">
                        <span class=""visually-hidden"">Loading...</span>
                    </div>
                </div>";

            output.Content.SetHtmlContent(tableHtml);
        }
    }

    /// <summary>
    /// Tag helper for HTMX modal with ABP integration
    /// </summary>
    [HtmlTargetElement("htmx-modal")]
    public class HtmxModalTagHelper : TagHelper
    {
        [HtmlAttributeName("modal-id")]
        public string ModalId { get; set; } = "htmx-modal";

        [HtmlAttributeName("size")]
        public string Size { get; set; } = "modal-lg";

        public override void Process(TagHelperContext context, TagHelperOutput output)
        {
            output.TagName = "div";
            output.Attributes.SetAttribute("class", "modal fade");
            output.Attributes.SetAttribute("id", ModalId);
            output.Attributes.SetAttribute("tabindex", "-1");
            output.Attributes.SetAttribute("aria-hidden", "true");

            var modalHtml = $@"
                <div class=""modal-dialog {Size}"">
                    <div class=""modal-content"" id=""{ModalId}-content"">
                        <!-- Modal content will be loaded via HTMX -->
                    </div>
                </div>";

            output.Content.SetHtmlContent(modalHtml);
        }
    }
}
