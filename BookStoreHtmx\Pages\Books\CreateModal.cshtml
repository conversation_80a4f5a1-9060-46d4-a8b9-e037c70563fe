@page
@using Microsoft.AspNetCore.Mvc.Localization
@using BookStoreHtmx.Localization
@using Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Modal
@using BookStoreHtmx.Web.Pages.Books
@using BookStoreHtmx.Books;
@using System.Globalization
@inject IHtmlLocalizer<BookStoreHtmxResource> L
@model CreateModalModel
@{
    Layout = null;
}


<form data-ajaxForm="true" asp-page="/Books/CreateModal" autocomplete="off">
    <abp-modal id="BookCreateModal">
        <abp-modal-header title="@L["NewBook"].Value"></abp-modal-header>

        <abp-modal-body>

                
                    
                    

            <abp-input asp-for="Book.Name"    />


            <abp-input asp-for="Book.PublishDate"    type="date" value="@DateTime.Now"/>


            <abp-input asp-for="Book.Price"   data-val="false" />


            <abp-input asp-for="Book.Description" text-area   />

                                <label for="Author_FullName">@L["Author"] *</label>
            <div class="input-group mb-3">
                <input hidden value="@(Model.Book.AuthorId  == default ? "": Model.Book.AuthorId.ToString()
)" id="Author_Id" name="Book.AuthorId"/>
                <input type="text" id="Author_FullName" class="form-control" disabled>
                <abp-button button-type="Info" id="AuthorLookupOpenButton" class="text-light">@L["Pick"]</abp-button>
                <abp-button button-type="Danger" class="lookupCleanButton ms-1"><i class="fa fa-times"></i></abp-button>
            </div>

                


        </abp-modal-body>

        <abp-modal-footer buttons="@(AbpModalButtons.Cancel|AbpModalButtons.Save)">

        </abp-modal-footer>
    </abp-modal>
</form>
