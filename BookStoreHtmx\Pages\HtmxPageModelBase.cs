using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Volo.Abp.AspNetCore.Mvc.UI.RazorPages;
using BookStoreHtmx.Extensions;
using System.Text.Json;

namespace BookStoreHtmx.Pages
{
    /// <summary>
    /// Base page model class that provides HTMX integration for ABP Framework pages
    /// </summary>
    public abstract class HtmxPageModelBase : AbpPageModel
    {
        /// <summary>
        /// Checks if the current request is an HTMX request
        /// </summary>
        protected bool IsHtmxRequest => Request.IsHtmxRequest();

        /// <summary>
        /// Checks if the current request is an HTMX boosted request
        /// </summary>
        protected bool IsHtmxBoosted => Request.IsHtmxBoosted();

        /// <summary>
        /// Gets the HTMX trigger element ID
        /// </summary>
        protected string HtmxTrigger => Request.GetHtmxTrigger();

        /// <summary>
        /// Gets the HTMX target element ID
        /// </summary>
        protected string HtmxTarget => Request.GetHtmxTarget();

        /// <summary>
        /// Returns a partial view for HTMX requests, full page for regular requests
        /// </summary>
        protected IActionResult HtmxPartialOrPage(string partialViewName = null, object model = null)
        {
            if (IsHtmxRequest)
            {
                return Partial(partialViewName ?? ViewData["PartialViewName"]?.ToString(), model ?? Model);
            }
            return Page();
        }

        /// <summary>
        /// Sets HTMX trigger event in response headers
        /// </summary>
        protected void SetHtmxTrigger(string eventName, object eventData = null)
        {
            var triggerData = eventData != null ? JsonSerializer.Serialize(eventData) : "true";
            Response.Headers.Add("HX-Trigger", $"{{\"{eventName}\": {triggerData}}}");
        }

        /// <summary>
        /// Sets HTMX redirect header
        /// </summary>
        protected void SetHtmxRedirect(string url)
        {
            Response.Headers.Add("HX-Redirect", url);
        }

        /// <summary>
        /// Sets HTMX refresh header
        /// </summary>
        protected void SetHtmxRefresh()
        {
            Response.Headers.Add("HX-Refresh", "true");
        }

        /// <summary>
        /// Closes modal and optionally triggers other events
        /// </summary>
        protected void CloseModalAndRefresh(string refreshTarget = null, string successMessage = null)
        {
            var triggerEvents = new Dictionary<string, object>
            {
                ["closeModal"] = true
            };

            if (!string.IsNullOrEmpty(refreshTarget))
            {
                triggerEvents["refreshTarget"] = refreshTarget;
            }

            if (!string.IsNullOrEmpty(successMessage))
            {
                triggerEvents["showNotification"] = new { type = "success", message = successMessage };
            }

            Response.Headers.Add("HX-Trigger", JsonSerializer.Serialize(triggerEvents));
        }

        /// <summary>
        /// Returns success response for HTMX requests
        /// </summary>
        protected IActionResult HtmxSuccess(string message = null, object data = null)
        {
            if (!string.IsNullOrEmpty(message))
            {
                SetHtmxTrigger("showNotification", new { type = "success", message = message });
            }

            return new JsonResult(new { success = true, message = message, data = data });
        }

        /// <summary>
        /// Returns error response for HTMX requests
        /// </summary>
        protected IActionResult HtmxError(string message, object errors = null)
        {
            SetHtmxTrigger("showNotification", new { type = "error", message = message });

            if (IsHtmxRequest)
            {
                Response.StatusCode = 400; // Bad Request for validation errors
            }

            return new JsonResult(new { success = false, message = message, errors = errors });
        }

        /// <summary>
        /// Returns validation error response with model state errors
        /// </summary>
        protected IActionResult HtmxValidationError(string message = "Please correct the errors and try again.")
        {
            var errors = new Dictionary<string, string[]>();

            foreach (var modelError in ModelState)
            {
                var key = modelError.Key;
                var errorMessages = modelError.Value.Errors.Select(e => e.ErrorMessage).ToArray();
                if (errorMessages.Length > 0)
                {
                    errors[key] = errorMessages;
                }
            }

            if (IsHtmxRequest)
            {
                Response.StatusCode = 400; // Bad Request for validation errors
                return new JsonResult(new { success = false, message = message, errors = errors });
            }

            Alerts.Danger(message);
            return Page();
        }

        /// <summary>
        /// Handles form submission results for both HTMX and regular requests
        /// </summary>
        protected IActionResult HandleFormResult(bool isSuccess, string successMessage = null, string errorMessage = null, string redirectUrl = null)
        {
            if (isSuccess)
            {
                if (IsHtmxRequest)
                {
                    CloseModalAndRefresh("#data-table", successMessage);
                    return Content("");
                }
                else
                {
                    if (!string.IsNullOrEmpty(successMessage))
                    {
                        Alerts.Success(successMessage);
                    }
                    return RedirectToPage(redirectUrl ?? "./Index");
                }
            }
            else
            {
                if (IsHtmxRequest)
                {
                    return HtmxError(errorMessage ?? "An error occurred while processing your request.");
                }
                else
                {
                    if (!string.IsNullOrEmpty(errorMessage))
                    {
                        Alerts.Danger(errorMessage);
                    }
                    return Page();
                }
            }
        }

        /// <summary>
        /// Creates a paginated response for HTMX table requests
        /// </summary>
        protected IActionResult HtmxTableResponse<T>(IEnumerable<T> data, int totalCount, int pageIndex, int pageSize)
        {
            var response = new HtmxTableResponse<T>
            {
                Data = data,
                TotalCount = totalCount,
                PageIndex = pageIndex,
                PageSize = pageSize
            };

            if (IsHtmxRequest)
            {
                return Partial("_TableRows", response);
            }

            return new JsonResult(response);
        }

        /// <summary>
        /// Validates model state and returns appropriate response
        /// </summary>
        protected IActionResult ValidateAndProcess(Func<IActionResult> successAction, string errorMessage = "Please correct the errors and try again.")
        {
            if (!ModelState.IsValid)
            {
                return HtmxValidationError(errorMessage);
            }

            try
            {
                return successAction();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error processing request");

                if (IsHtmxRequest)
                {
                    return HtmxError(errorMessage);
                }

                Alerts.Danger(errorMessage);
                return Page();
            }
        }

        /// <summary>
        /// Validates model state and processes async action
        /// </summary>
        protected async Task<IActionResult> ValidateAndProcessAsync(Func<Task<IActionResult>> successAction, string errorMessage = "Please correct the errors and try again.")
        {
            if (!ModelState.IsValid)
            {
                return HtmxValidationError(errorMessage);
            }

            try
            {
                return await successAction();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error processing request");

                if (IsHtmxRequest)
                {
                    return HtmxError(errorMessage);
                }

                Alerts.Danger(errorMessage);
                return Page();
            }
        }
    }
}
