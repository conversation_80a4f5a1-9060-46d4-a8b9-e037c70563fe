﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;

namespace BookStoreHtmx.Data;

public class BookStoreHtmxDbContextFactory : IDesignTimeDbContextFactory<BookStoreHtmxDbContext>
{
    public BookStoreHtmxDbContext CreateDbContext(string[] args)
    {
        BookStoreHtmxEfCoreEntityExtensionMappings.Configure();
        var configuration = BuildConfiguration();

        var builder = new DbContextOptionsBuilder<BookStoreHtmxDbContext>()
            .UseSqlServer(configuration.GetConnectionString("Default"));

        return new BookStoreHtmxDbContext(builder.Options);
    }

    private static IConfigurationRoot BuildConfiguration()
    {
        var builder = new ConfigurationBuilder()
            .SetBasePath(Directory.GetCurrentDirectory())
            .AddJsonFile("appsettings.json", optional: false);

        return builder.Build();
    }
}