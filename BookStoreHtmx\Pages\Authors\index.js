$(function () {
    var l = abp.localization.getResource("BookStoreHtmx");
	
	var authorService = window.bookStoreHtmx.authors.authors;
	
	
    var createModal = new abp.ModalManager({
        viewUrl: abp.appPath + "Authors/CreateModal",
        scriptUrl: abp.appPath + "Pages/Authors/createModal.js",
        modalClass: "authorCreate"
    });

	var editModal = new abp.ModalManager({
        viewUrl: abp.appPath + "Authors/EditModal",
        scriptUrl: abp.appPath + "Pages/Authors/editModal.js",
        modalClass: "authorEdit"
    });

	var getFilter = function() {
        return {
            filterText: $("#FilterText").val(),
            fullName: $("#FullNameFilter").val(),
			birthDateMin: $("#BirthDateFilterMin").val(),
			birthDateMax: $("#BirthDateFilterMax").val()
        };
    };
    
    
    
    var dataTableColumns = [
            {
                rowAction: {
                    items:
                        [
                            {
                                text: l("Edit"),
                                visible: abp.auth.isGranted('BookStoreHtmx.Authors.Edit'),
                                action: function (data) {
                                    editModal.open({
                                     id: data.record.id
                                     });
                                }
                            },
                            {
                                text: l("Delete"),
                                visible: abp.auth.isGranted('BookStoreHtmx.Authors.Delete'),
                                confirmMessage: function () {
                                    return l("DeleteConfirmationMessage");
                                },
                                action: function (data) {
                                    authorService.delete(data.record.id)
                                        .then(function () {
                                            abp.notify.success(l("SuccessfullyDeleted"));
                                            dataTable.ajax.reloadEx();;
                                        });
                                }
                            }
                        ]
                },
                width: "1rem"
            },
			{ data: "fullName" },
			{ data: "birthDate", dataFormat: 'date' }        
    ];
    
    
    
    

    var dataTable = $("#AuthorsTable").DataTable(abp.libs.datatables.normalizeConfiguration({
        processing: true,
        serverSide: true,
        paging: true,
        searching: false,
        scrollX: true,
        autoWidth: true,
        scrollCollapse: true,
        order: [[0, "desc"]],
        ajax: abp.libs.datatables.createAjax(authorService.getList, getFilter),
        columnDefs: dataTableColumns
    }));
    
    
    
    

    createModal.onResult(function () {
        dataTable.ajax.reloadEx();;
        
        
    });

    editModal.onResult(function () {
        dataTable.ajax.reloadEx();;
        
                
    });

    $("#NewAuthorButton").click(function (e) {
        e.preventDefault();
        createModal.open();
    });

	$("#SearchForm").submit(function (e) {
        e.preventDefault();
        dataTable.ajax.reloadEx();;
        
        
    });

    $('#AdvancedFilterSectionToggler').on('click', function (e) {
        $('#AdvancedFilterSection').toggle();
        var iconCss = $("#AdvancedFilterSection").is(":visible") ? "fa ms-1 fa-angle-up" : "fa ms-1 fa-angle-down";
        $(this).find("i").attr("class", iconCss);
    });

    $('#AdvancedFilterSection').on('keypress', function (e) {
        if (e.which === 13) {
            dataTable.ajax.reloadEx();
            
            
        }
    });

    $('#AdvancedFilterSection select').change(function() {
        dataTable.ajax.reloadEx();
        
        
    });
    
    
    
    
    
    
    
    
    
    
});
