using Volo.Abp.Application.Dtos;
using System;

namespace BookStoreHtmx.Authors
{
    public class GetAuthorsInput : PagedAndSortedResultRequestDto
    {
        public string? FilterText { get; set; }

        public string? FullName { get; set; }
        public DateOnly? BirthDateMin { get; set; }
        public DateOnly? BirthDateMax { get; set; }

        public GetAuthorsInput()
        {

        }
    }
}