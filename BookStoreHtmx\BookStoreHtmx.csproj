<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <GenerateEmbeddedFilesManifest>true</GenerateEmbeddedFilesManifest>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <NoWarn>$(NoWarn);CS1591</NoWarn>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="MiniExcel" Version="1.41.1" />
    <PackageReference Include="Serilog.AspNetCore" Version="9.0.0" />
    <PackageReference Include="Serilog.Sinks.Async" Version="2.1.0" />
    <PackageReference Include="Microsoft.Extensions.FileProviders.Embedded" Version="9.0.4" />
    <PackageReference Include="AspNetCore.HealthChecks.UI" Version="9.0.0" />
    <PackageReference Include="AspNetCore.HealthChecks.UI.Client" Version="9.0.0" />
    <PackageReference Include="AspNetCore.HealthChecks.UI.InMemory.Storage" Version="9.0.0" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.AspNetCore.Mvc" Version="9.2.1" />
    <PackageReference Include="Volo.Abp.Autofac" Version="9.2.1" />
    <PackageReference Include="Volo.Abp.AutoMapper" Version="9.2.1" />
    <PackageReference Include="Volo.Abp.Swashbuckle" Version="9.2.1" />
    <PackageReference Include="Volo.Abp.AspNetCore.Serilog" Version="9.2.1" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Authentication.Google" Version="9.0.4" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.MicrosoftAccount" Version="9.0.4" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.Twitter" Version="9.0.4" />
    
    <PackageReference Include="Volo.Abp.Account.Pro.Admin.Web" Version="9.2.1" />
    <PackageReference Include="Volo.Abp.Account.Pro.Admin.HttpApi" Version="9.2.1" />
    <PackageReference Include="Volo.Abp.Account.Pro.Admin.Application" Version="9.2.1" />

    <PackageReference Include="Volo.Abp.Account.Pro.Public.Web.OpenIddict" Version="9.2.1" />
    <PackageReference Include="Volo.Abp.Account.Pro.Public.HttpApi" Version="9.2.1" />
    <PackageReference Include="Volo.Abp.Account.Pro.Public.Application" Version="9.2.1" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.PermissionManagement.Domain.Identity" Version="9.2.1" />
    <PackageReference Include="Volo.Abp.Identity.Pro.Web" Version="9.2.1" />
    <PackageReference Include="Volo.Abp.Identity.Pro.HttpApi" Version="9.2.1" />
    <PackageReference Include="Volo.Abp.Identity.Pro.Application" Version="9.2.1" />
    <PackageReference Include="Volo.Abp.OpenIddict.Pro.Web" Version="9.2.1" />
    <PackageReference Include="Volo.Abp.OpenIddict.Pro.HttpApi" Version="9.2.1" />
    <PackageReference Include="Volo.Abp.OpenIddict.Pro.Application" Version="9.2.1" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.PermissionManagement.Domain.OpenIddict" Version="9.2.1" />
    <PackageReference Include="Volo.Abp.PermissionManagement.Web" Version="9.2.1" />
    <PackageReference Include="Volo.Abp.PermissionManagement.HttpApi" Version="9.2.1" />
    <PackageReference Include="Volo.Abp.PermissionManagement.Application" Version="9.2.1" />
  </ItemGroup>


  <ItemGroup>
    <PackageReference Include="Volo.Abp.LanguageManagement.Web" Version="9.2.1" />
    <PackageReference Include="Volo.Abp.LanguageManagement.HttpApi" Version="9.2.1" />
    <PackageReference Include="Volo.Abp.LanguageManagement.Application" Version="9.2.1" />
  </ItemGroup>
  
  <ItemGroup>
    <PackageReference Include="Volo.Abp.TextTemplateManagement.Web" Version="9.2.1" />
    <PackageReference Include="Volo.Abp.TextTemplateManagement.HttpApi" Version="9.2.1" />
    <PackageReference Include="Volo.Abp.TextTemplateManagement.Application" Version="9.2.1" />
  </ItemGroup>
  
  <ItemGroup>
    <PackageReference Include="Volo.Abp.AuditLogging.Web" Version="9.2.1" />
    <PackageReference Include="Volo.Abp.AuditLogging.HttpApi" Version="9.2.1" />
    <PackageReference Include="Volo.Abp.AuditLogging.Application" Version="9.2.1" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Volo.Abp.LeptonXTheme.Management.Web" Version="4.2.1" />
    <PackageReference Include="Volo.Abp.LeptonXTheme.Management.HttpApi" Version="4.2.1" />
    <PackageReference Include="Volo.Abp.LeptonXTheme.Management.Application" Version="4.2.1" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.FeatureManagement.Web" Version="9.2.1" />
    <PackageReference Include="Volo.Abp.FeatureManagement.HttpApi" Version="9.2.1" />
    <PackageReference Include="Volo.Abp.FeatureManagement.Application" Version="9.2.1" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.SettingManagement.Web" Version="9.2.1" />
    <PackageReference Include="Volo.Abp.SettingManagement.HttpApi" Version="9.2.1" />
    <PackageReference Include="Volo.Abp.SettingManagement.Application" Version="9.2.1" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.AspNetCore.Mvc.UI.Theme.LeptonX" Version="4.2.1" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.AuditLogging.EntityFrameworkCore" Version="9.2.1" />
    <PackageReference Include="Volo.Abp.LanguageManagement.EntityFrameworkCore" Version="9.2.1" />
    <PackageReference Include="Volo.Abp.TextTemplateManagement.EntityFrameworkCore" Version="9.2.1" />
    <PackageReference Include="Volo.Abp.Identity.Pro.EntityFrameworkCore" Version="9.2.1" />
    <PackageReference Include="Volo.Abp.OpenIddict.Pro.EntityFrameworkCore" Version="9.2.1" />
    <PackageReference Include="Volo.Abp.BackgroundJobs.EntityFrameworkCore" Version="9.2.1" />
    <PackageReference Include="Volo.Abp.BlobStoring.Database.EntityFrameworkCore" Version="9.2.1" />
    <PackageReference Include="Volo.Abp.FeatureManagement.EntityFrameworkCore" Version="9.2.1" />
    <PackageReference Include="Volo.Abp.PermissionManagement.EntityFrameworkCore" Version="9.2.1" />
    <PackageReference Include="Volo.Abp.SettingManagement.EntityFrameworkCore" Version="9.2.1" />
    <PackageReference Include="Volo.Abp.EntityFrameworkCore.SqlServer" Version="9.2.1" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.Studio.Client.AspNetCore" Version="1.0.2" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.Commercial.SuiteTemplates" Version="9.2.1" />
  </ItemGroup>
  
  <ItemGroup>
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.4">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers</IncludeAssets>
      <PrivateAssets>compile; contentFiles; build; buildMultitargeting; buildTransitive; analyzers; native</PrivateAssets>
    </PackageReference>
  </ItemGroup>
  
  <ItemGroup Condition="Exists('./openiddict.pfx')">
    <None Remove="openiddict.pfx" />
    <EmbeddedResource Include="openiddict.pfx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
  </ItemGroup>

  <ItemGroup>
    <Content Remove="Localization\BookStoreHtmx\*.json" />
    <EmbeddedResource Include="Localization\BookStoreHtmx\*.json" />
  </ItemGroup>

  <ItemGroup>
    <Compile Remove="Logs\**" />
    <Content Remove="Logs\**" />
    <EmbeddedResource Remove="Logs\**" />
    <None Remove="Logs\**" />
  </ItemGroup>

  <ItemGroup>
    <None Update="Pages\**\*.js">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="Pages\**\*.css">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>

  <ItemGroup>
    <None Remove="**\*.abppkg" />
    <None Remove="**\*.abppkg.analyze.json" />
    <Content Remove="$(UserProfile)\.nuget\packages\*\*\contentFiles\any\*\*.abppkg*" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Controllers\" />
    <Folder Include="Pages\Shared\" />
  </ItemGroup>

</Project>
