using Volo.Abp.AspNetCore.Mvc.UI.RazorPages;
using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using BookStoreHtmx.Pages;
using BookStoreHtmx.Extensions;
using BookStoreHtmx.Authors;
using System.Collections.Generic;
using System.Linq;

namespace BookStoreHtmx.Web.Pages.Shared
{
    public class LookupModalModel : HtmxPageModelBase
    {
        public string CurrentId { get; set; } = string.Empty;
        public string CurrentDisplayName { get; set; } = string.Empty;
        public string ServiceMethod { get; set; } = string.Empty;
        public string FilterText { get; set; } = string.Empty;

        private readonly IAuthorsAppService _authorsAppService;

        public LookupModalModel(IAuthorsAppService authorsAppService)
        {
            _authorsAppService = authorsAppService;
        }

        public virtual Task OnGetAsync(string serviceMethod = "", string currentId = "", string currentDisplayName = "", string filterText = "")
        {
            ServiceMethod = serviceMethod ?? string.Empty;
            CurrentId = currentId ?? string.Empty;
            CurrentDisplayName = currentDisplayName ?? string.Empty;
            FilterText = filterText ?? string.Empty;

            return Task.CompletedTask;
        }

        public virtual async Task<IActionResult> OnGetTableDataAsync(string serviceMethod = "", string currentId = "", string filterText = "", int pageIndex = 1, int pageSize = 10)
        {
            try
            {
                if (serviceMethod == "getAuthorLookup")
                {
                    var input = new GetAuthorsInput
                    {
                        FilterText = filterText,
                        MaxResultCount = pageSize,
                        SkipCount = (pageIndex - 1) * pageSize,
                        Sorting = "fullName"
                    };

                    var result = await _authorsAppService.GetListAsync(input);

                    var lookupData = result.Items.Select(x => new LookupItemDto
                    {
                        Id = x.Id.ToString(),
                        DisplayName = x.FullName
                    }).ToList();

                    var response = new HtmxTableResponse<LookupItemDto>
                    {
                        Data = lookupData,
                        TotalCount = (int)result.TotalCount,
                        PageIndex = pageIndex,
                        PageSize = pageSize
                    };

                    return Partial("_LookupTableRows", response);
                }

                return Partial("_LookupTableRows", new HtmxTableResponse<LookupItemDto>
                {
                    Data = new List<LookupItemDto>(),
                    TotalCount = 0,
                    PageIndex = 1,
                    PageSize = pageSize
                });
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error loading lookup data");
                return HtmxError("Error loading data");
            }
        }
    }

    public class LookupItemDto
    {
        public string Id { get; set; } = string.Empty;
        public string DisplayName { get; set; } = string.Empty;
    }
}