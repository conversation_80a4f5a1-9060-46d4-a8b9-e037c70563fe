# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@abp/aspnetcore.mvc.ui.theme.shared@~9.2.2":
  version "9.2.2"
  resolved "https://registry.yarnpkg.com/@abp/aspnetcore.mvc.ui.theme.shared/-/aspnetcore.mvc.ui.theme.shared-9.2.2.tgz#f852a8f3aa66b638378db798c03679122b4ba5a5"
  integrity sha512-yP3JCJ2mcmhVIDEL9/hispM07FowSYr/VvM6ohv1daMQfyLmciIXyQPgoQwhHEWXifJvytKhbQauDxW3B3L7lw==
  dependencies:
    "@abp/aspnetcore.mvc.ui" "~9.2.2"
    "@abp/bootstrap" "~9.2.2"
    "@abp/bootstrap-datepicker" "~9.2.2"
    "@abp/bootstrap-daterangepicker" "~9.2.2"
    "@abp/datatables.net-bs5" "~9.2.2"
    "@abp/font-awesome" "~9.2.2"
    "@abp/jquery-form" "~9.2.2"
    "@abp/jquery-validation-unobtrusive" "~9.2.2"
    "@abp/lodash" "~9.2.2"
    "@abp/luxon" "~9.2.2"
    "@abp/malihu-custom-scrollbar-plugin" "~9.2.2"
    "@abp/moment" "~9.2.2"
    "@abp/select2" "~9.2.2"
    "@abp/sweetalert2" "~9.2.2"
    "@abp/timeago" "~9.2.2"

"@abp/aspnetcore.mvc.ui@~9.2.2":
  version "9.2.2"
  resolved "https://registry.yarnpkg.com/@abp/aspnetcore.mvc.ui/-/aspnetcore.mvc.ui-9.2.2.tgz#0349556ce125bf83e88ce8384ff1d1e648c9766a"
  integrity sha512-Ll24xOpccgIZtnCRKrIj5AygRrV0fBCVqKvR16VwiWwMPd86N60Vflb9LyU7lx9/1sFMp/ONbGojxDy/tG9aBQ==
  dependencies:
    ansi-colors "^4.1.3"

"@abp/bootstrap-datepicker@~9.2.2":
  version "9.2.2"
  resolved "https://registry.yarnpkg.com/@abp/bootstrap-datepicker/-/bootstrap-datepicker-9.2.2.tgz#75aefaa4a2f0b322012599d92caa5c7a0b88cd67"
  integrity sha512-6ogV4COwnGmAznEOG64qQUfoyYvmMvoi4fwAqOcKrNjWUcVFUa+wL+fbVDf9tbYT3SkeoNG5sD0JN7xj43V5cg==
  dependencies:
    bootstrap-datepicker "^1.10.0"

"@abp/bootstrap-daterangepicker@~9.2.2":
  version "9.2.2"
  resolved "https://registry.yarnpkg.com/@abp/bootstrap-daterangepicker/-/bootstrap-daterangepicker-9.2.2.tgz#5493d00116aa29404873ff551aa86f2605bb19f2"
  integrity sha512-/JYaSj/LLaVYaeqihVS8jZb0K7J40SLakwVCFXwFhAB+/MSfgdGmzJxdf8nv/2GYlIZtG1Bv2gaQ8kHjd+M7DA==
  dependencies:
    bootstrap-daterangepicker "^3.1.0"

"@abp/bootstrap@~9.2.2":
  version "9.2.2"
  resolved "https://registry.yarnpkg.com/@abp/bootstrap/-/bootstrap-9.2.2.tgz#80a7e43c3629ca9db3bedd6fb28b69f5e4d90685"
  integrity sha512-1PI1sRzLy1aWcv+JO8mYl5/sRfbUq2pXAwWcRb80axJSOfb36Uhw+yURRcEaaw7bAtNN7qLvf0EBKS9Rh3iTrQ==
  dependencies:
    "@abp/core" "~9.2.2"
    bootstrap "^5.3.3"

"@abp/chart.js@~9.2.2":
  version "9.2.2"
  resolved "https://registry.yarnpkg.com/@abp/chart.js/-/chart.js-9.2.2.tgz#e4dad4456b8af1847a08a88104c141562c937f14"
  integrity sha512-wMxIjlJgb4ORW9GUV/YoR5jd/Na53Zic4VsoFGUuHNwqpPsse/03Ds87mpQFDl2/jyXy2kkSgBvCCew6XH6ovA==
  dependencies:
    chart.js "^4.4.4"

"@abp/core@~9.2.2":
  version "9.2.2"
  resolved "https://registry.yarnpkg.com/@abp/core/-/core-9.2.2.tgz#d1fc363ddb26b4118e14d277f32a26247be4d430"
  integrity sha512-YR5XPW0JED/hca8aGiWd73pR6u93Gs6IfklpU0DnSrOFTm2FnoNAryPujGUyGl0ZJM8G7Rt0xwQyAKV6PAqsXg==
  dependencies:
    "@abp/utils" "~9.2.2"

"@abp/cropperjs@~9.2.2":
  version "9.2.2"
  resolved "https://registry.yarnpkg.com/@abp/cropperjs/-/cropperjs-9.2.2.tgz#a60d6d58d3f79524b5217e49d0328b9cfea9cdcd"
  integrity sha512-xKD+RgYTcAQw2l2XDUz1iaARrUnmIC+0vEQlwyXXxKLGfE5/Jt7xvzhIlDU38iwS5JtT6q/Q/4QG1/yjR00gXQ==
  dependencies:
    "@abp/core" "~9.2.2"
    cropperjs "^1.6.2"

"@abp/datatables.net-bs5@~9.2.2":
  version "9.2.2"
  resolved "https://registry.yarnpkg.com/@abp/datatables.net-bs5/-/datatables.net-bs5-9.2.2.tgz#197f99726c3bcaefaefdb9c91d07b50052340871"
  integrity sha512-BVWCOrPx7B3JMnxZ/ZatSfv2NRRWQXhL85DFkKE92k5+ZE+Fzx2noa3SgwAsuVXmg8Q7zVg5xRM4w7hDwBW8Bw==
  dependencies:
    "@abp/datatables.net" "~9.2.2"
    datatables.net-bs5 "^2.1.8"

"@abp/datatables.net@~9.2.2":
  version "9.2.2"
  resolved "https://registry.yarnpkg.com/@abp/datatables.net/-/datatables.net-9.2.2.tgz#334104f11d2da98df4afaf03b53a2cd504c7068f"
  integrity sha512-2tJxpmsh5zaVpbz0TWwpd+cdS1auwpURRfbNZ6xDM8cL/FS2dTBv8RGBIsKU0xltdfzKmtSd/Uh0xVjJjkNN2Q==
  dependencies:
    "@abp/jquery" "~9.2.2"
    datatables.net "^2.1.8"

"@abp/flag-icon-css@~9.2.2":
  version "9.2.2"
  resolved "https://registry.yarnpkg.com/@abp/flag-icon-css/-/flag-icon-css-9.2.2.tgz#d3b2e9dfb171ab5b50f25163c89608c3a0e0cb85"
  integrity sha512-zbduqu1FrfXKvswhh3V1wv2kFMOoSNPPHuV4gr/mrZfUy5LrLo+URnLj/5kI45sQT210nUZyTBD8k0lKZG0UzQ==
  dependencies:
    flag-icon-css "^4.1.7"

"@abp/font-awesome@~9.2.2":
  version "9.2.2"
  resolved "https://registry.yarnpkg.com/@abp/font-awesome/-/font-awesome-9.2.2.tgz#f978cd07eb8175f73e252c345a9765cdf35b4d3d"
  integrity sha512-Zoc5HoqFqltc4/nudGFhL/Mr0UtMP15BjzLqbMJX0XFjHVzAg9oIGeC/kWFqUPWrQ+M5xGLm4kznNuyzee7Dnw==
  dependencies:
    "@abp/core" "~9.2.2"
    "@fortawesome/fontawesome-free" "^6.6.0"

"@abp/jquery-form@~9.2.2":
  version "9.2.2"
  resolved "https://registry.yarnpkg.com/@abp/jquery-form/-/jquery-form-9.2.2.tgz#f958f05b66e63bc7b0fe6ed7bf2c5b25e8e5c244"
  integrity sha512-Plo30NgmoZETQvtO2h9UkfXl8dcsdNvG9G0a7AENkj/fszJDP4J5T2U2XkPG4G6wbOg4h8gsmIceV++bCFXfgw==
  dependencies:
    "@abp/jquery" "~9.2.2"
    jquery-form "^4.3.0"

"@abp/jquery-validation-unobtrusive@~9.2.2":
  version "9.2.2"
  resolved "https://registry.yarnpkg.com/@abp/jquery-validation-unobtrusive/-/jquery-validation-unobtrusive-9.2.2.tgz#6fe634b9032c9e40198f3e0f27cc57afb3266d29"
  integrity sha512-VXRNSky8nVtH0NHn189HvwitKPaLzfbYoMEM7QFf1n3mEbkoBp4P43JbuTJdAzyRm5KNPNewMl5HW8EnZB0krQ==
  dependencies:
    "@abp/jquery-validation" "~9.2.2"
    jquery-validation-unobtrusive "^4.0.0"

"@abp/jquery-validation@~9.2.2":
  version "9.2.2"
  resolved "https://registry.yarnpkg.com/@abp/jquery-validation/-/jquery-validation-9.2.2.tgz#50a72f32d1c113e67cf75effefe7f1501d5956bd"
  integrity sha512-mHfPYSBkBSxytaJcbRbHtyeojGmiHiW825qFyyIBwr2kCY0s6Jr3zW8rtm3bIu2EB64cITI7kp7yKyJOxgQYlQ==
  dependencies:
    "@abp/jquery" "~9.2.2"
    jquery-validation "^1.21.0"

"@abp/jquery@~9.2.2":
  version "9.2.2"
  resolved "https://registry.yarnpkg.com/@abp/jquery/-/jquery-9.2.2.tgz#42c092c1790d03cdc4578d8cbeaa1954cb843cf7"
  integrity sha512-NavJF7SToheI+3VQWbZe+5cZVMBDt4hSGzclfB/r2/+YdKerxgggv9tDp6w8PuvnhuxHmZp6MIPelKCFN0B3Tw==
  dependencies:
    "@abp/core" "~9.2.2"
    jquery "~3.7.1"

"@abp/jstree@~9.2.2":
  version "9.2.2"
  resolved "https://registry.yarnpkg.com/@abp/jstree/-/jstree-9.2.2.tgz#b167f39c35b3517dd1313c3f39b948ebf9c7766f"
  integrity sha512-hxUe20oTqQgAZ33qtA2fqo68TB4RSvXD7cAXlXpZJKjmVTjUjiCar+52dl4GaiS3/rfYYlxAESHdMo94DT3uJg==
  dependencies:
    "@abp/jquery" "~9.2.2"
    jstree "^3.3.17"

"@abp/lodash@~9.2.2":
  version "9.2.2"
  resolved "https://registry.yarnpkg.com/@abp/lodash/-/lodash-9.2.2.tgz#41ec734ec769f22f5bebcea0b783d2b47ccb6d9a"
  integrity sha512-0uvG9hGBwp0hg5sgh0NC3URIPAD0sSfVgMN39wFJdfA+snrd4fb9TqRhZ+ntvK7iGdCEx6iIHGIgOTTzkpBB6Q==
  dependencies:
    "@abp/core" "~9.2.2"
    lodash "^4.17.21"

"@abp/luxon@~9.2.2":
  version "9.2.2"
  resolved "https://registry.yarnpkg.com/@abp/luxon/-/luxon-9.2.2.tgz#553d69dc8291a1e53b5760dacf6a9067f0e54862"
  integrity sha512-3J4Ch4lteckGD5n9tm5qqDydxASAD22z8z86/wfKR91y+JlXRMFTki6vU8vVnDzW4jVN+i+pUj2tg9Ov2buikQ==
  dependencies:
    "@abp/core" "~9.2.2"
    luxon "^3.5.0"

"@abp/malihu-custom-scrollbar-plugin@~9.2.2":
  version "9.2.2"
  resolved "https://registry.yarnpkg.com/@abp/malihu-custom-scrollbar-plugin/-/malihu-custom-scrollbar-plugin-9.2.2.tgz#97af3474245425b531200a4970c2e4f110a84deb"
  integrity sha512-siudEyOumlFV/94CABziPHWVg/C4+QjZdEqzsClPCQg+73PFNIiSwKLR7IyeBOHd+1Nf6sqNeOOmWpcGgGFe2g==
  dependencies:
    "@abp/core" "~9.2.2"
    malihu-custom-scrollbar-plugin "^3.1.5"

"@abp/moment@~9.2.2":
  version "9.2.2"
  resolved "https://registry.yarnpkg.com/@abp/moment/-/moment-9.2.2.tgz#4c6d1752705678a23044aabb1316537e3edfc54c"
  integrity sha512-xi07p1rj673qAtLwnSAmY/7DE5YfnAMvVRD8O0g+NrXiGbC1Zfv0iwhaXKTOzmUF8R+cVD/Ca3hEajqs8jHF0w==
  dependencies:
    moment "^2.30.1"

"@abp/qrcode@~9.2.2":
  version "9.2.2"
  resolved "https://registry.yarnpkg.com/@abp/qrcode/-/qrcode-9.2.2.tgz#e7bb45a2f68a502c30a57995e05eafc1e63f6c5b"
  integrity sha512-ZUAyTIsfGLEGzgayl8bL/+zzIEdF62rzF9kYJ9zrAh7RfnMDTHt257nDdJ//enfBsuTQjO8s2ser91fqeRVEdA==
  dependencies:
    "@abp/core" "~9.2.2"

"@abp/select2@~9.2.2":
  version "9.2.2"
  resolved "https://registry.yarnpkg.com/@abp/select2/-/select2-9.2.2.tgz#af5a9b7411be257f57b192fe2cabaa4915fa3b78"
  integrity sha512-UvEPO0KPXFdj4qUw1zkSu0z4/gj+54OpPBQuSLZmErXt8Mswz/XWdNZ5SqEpJdMzeHLWmFuGndo5J5EeJ+u29A==
  dependencies:
    "@abp/core" "~9.2.2"
    select2 "^4.0.13"

"@abp/sweetalert2@~9.2.2":
  version "9.2.2"
  resolved "https://registry.yarnpkg.com/@abp/sweetalert2/-/sweetalert2-9.2.2.tgz#d82e4363d7b9ff7b372b6b7f7db3da85a35b8d3e"
  integrity sha512-/+xSR+6T6cJqurJz7AgtuJj0V9TjkQiVNuN8CpZYHUjiRHIclkeGIAk7xZOH2/I/q9Ijy6cAwcx23vSP1aj00Q==
  dependencies:
    "@abp/core" "~9.2.2"
    sweetalert2 "^11.14.1"

"@abp/timeago@~9.2.2":
  version "9.2.2"
  resolved "https://registry.yarnpkg.com/@abp/timeago/-/timeago-9.2.2.tgz#688ff285031e473831d780ae9f046e902f11538f"
  integrity sha512-aWLlp8/ZYsqzyBF2OjlBhZoIKTNskuwSyNeH2YG6SPeNpvisDn7UN3aeThrBSmDWjKOksYFlHNW9Zmg8DC3wYw==
  dependencies:
    "@abp/jquery" "~9.2.2"
    timeago "^1.6.7"

"@abp/uppy@~9.2.2":
  version "9.2.2"
  resolved "https://registry.yarnpkg.com/@abp/uppy/-/uppy-9.2.2.tgz#1210f1a69aa7924bb35b616666248a62f1485166"
  integrity sha512-22ASdMfu2fBeuYOAGwZEQvmsH/1Ki3TUpOCjZLtiGFBzj4K/etDv4lZAVBY984yflC/H83viL8qSfUMEcSQQEg==
  dependencies:
    "@abp/core" "~9.2.2"
    uppy "^4.4.1"

"@abp/utils@~9.2.2":
  version "9.2.2"
  resolved "https://registry.yarnpkg.com/@abp/utils/-/utils-9.2.2.tgz#27873c943b29fee5f31b893cdd6f7d4c21b21913"
  integrity sha512-dHztqg4w4CgYwBecutG59dkQk3S6WoivZ7swS16C6sV3KO2chEiVjMKU36pw0XndAUtIzd0UiD5IFF6rkBuvJA==
  dependencies:
    just-compare "^2.3.0"

"@abp/zxcvbn@~9.2.2":
  version "9.2.2"
  resolved "https://registry.yarnpkg.com/@abp/zxcvbn/-/zxcvbn-9.2.2.tgz#43ea58c17a8073d4cc200b00c96d41a00de8cc48"
  integrity sha512-DYNPvsLFipS5TYpLoQZt8BJlvV2jC+9elbsZfj//kjMddd/fjiU+lWwGQM4yUpgVinlcuoNWoEU4NNGtDDPKBA==
  dependencies:
    "@abp/core" "~9.2.2"
    zxcvbn "^4.4.2"

"@fortawesome/fontawesome-free@^6.6.0":
  version "6.7.2"
  resolved "https://registry.yarnpkg.com/@fortawesome/fontawesome-free/-/fontawesome-free-6.7.2.tgz#8249de9b7e22fcb3ceb5e66090c30a1d5492b81a"
  integrity sha512-JUOtgFW6k9u4Y+xeIaEiLr3+cjoUPiAuLXoyKOJSia6Duzb7pq+A76P9ZdPDoAoxHdHzq6gE9/jKBGXlZT8FbA==

"@kurkle/color@^0.3.0":
  version "0.3.4"
  resolved "https://registry.yarnpkg.com/@kurkle/color/-/color-0.3.4.tgz#4d4ff677e1609214fc71c580125ddddd86abcabf"
  integrity sha512-M5UknZPHRu3DEDWoipU6sE8PdkZ6Z/S+v4dD+Ke8IaNlpdSQah50lz1KtcFBa2vsdOnwbbnxJwVM4wty6udA5w==

"@transloadit/prettier-bytes@^0.3.4":
  version "0.3.5"
  resolved "https://registry.yarnpkg.com/@transloadit/prettier-bytes/-/prettier-bytes-0.3.5.tgz#0cca83975293e3f4990229914942c69714122ede"
  integrity sha512-xF4A3d/ZyX2LJWeQZREZQw+qFX4TGQ8bGVP97OLRt6sPO6T0TNHBFTuRHOJh7RNmYOBmQ9MHxpolD9bXihpuVA==

"@types/retry@0.12.2":
  version "0.12.2"
  resolved "https://registry.yarnpkg.com/@types/retry/-/retry-0.12.2.tgz#ed279a64fa438bb69f2480eda44937912bb7480a"
  integrity sha512-XISRgDJ2Tc5q4TRqvgJtzsRkFYNJzZrhTdtMoGVBttwzzQJkPnS3WWTFc7kuDRoPtPakl+T+OfdEUjYJj7Jbow==

"@uppy/audio@^2.1.3":
  version "2.1.3"
  resolved "https://registry.yarnpkg.com/@uppy/audio/-/audio-2.1.3.tgz#a3baa16076efbff776ca3a409fd3073204f432b9"
  integrity sha512-aSeQ/n7h+9IaRb/L2vbTAd0UoTBYSoyFMoCAM6WbSCCuVbcf0CjkWn1x5yyxrv+mCehpz3hFRz+u4bg8O1Vmjg==
  dependencies:
    "@uppy/utils" "^6.1.4"
    preact "^10.5.13"

"@uppy/aws-s3@^4.2.3":
  version "4.2.3"
  resolved "https://registry.yarnpkg.com/@uppy/aws-s3/-/aws-s3-4.2.3.tgz#33ed4dbd7813ec2b07687213f48f05400d911738"
  integrity sha512-5vNgTE85DLujOXpzC6KEwJHLSi8o96v4rwZxMvDWQuikvX4sGcGflYjBCsPaVDYUCiiDXuhI8f93zfwCUEwQ/Q==
  dependencies:
    "@uppy/companion-client" "^4.4.1"
    "@uppy/utils" "^6.1.1"

"@uppy/box@^3.2.3":
  version "3.2.3"
  resolved "https://registry.yarnpkg.com/@uppy/box/-/box-3.2.3.tgz#4cc9a84c657e6cad2d3a52c6863112e33dfd0974"
  integrity sha512-h0aNbhV0IooCE9cg2TxP8T+QjPRa1vUGbv73+BJwDotgnl5DEyBN8Tzgrn8biU997L0SAuh6jrkSVVceeirJnQ==
  dependencies:
    "@uppy/companion-client" "^4.4.2"
    "@uppy/provider-views" "^4.4.3"
    "@uppy/utils" "^6.1.4"
    preact "^10.5.13"

"@uppy/companion-client@^4.4.1", "@uppy/companion-client@^4.4.2":
  version "4.4.2"
  resolved "https://registry.yarnpkg.com/@uppy/companion-client/-/companion-client-4.4.2.tgz#4e3cd9fb71f99ed62835db837006eff3d38fea87"
  integrity sha512-UZlHWItCGlZMlHxH4RgytUg43UZyuUX1JuvborNW1OzlLeZTvxL1dhjaq8pgjx3TlClkfpKLRRF8II0XPJgxzA==
  dependencies:
    "@uppy/utils" "^6.1.4"
    namespace-emitter "^2.0.1"
    p-retry "^6.1.0"

"@uppy/compressor@^2.2.1":
  version "2.2.1"
  resolved "https://registry.yarnpkg.com/@uppy/compressor/-/compressor-2.2.1.tgz#9324e3693f0766fa6d65b40b5fbbab81bd3c10b6"
  integrity sha512-2xWEHfmy/9HxOYZ1m11do69Gfa9Detz/zdfATlDZXlxUBhb9SKidea2FdZrea7gxFJjcBH9hO9tPK9k4ev+YJA==
  dependencies:
    "@transloadit/prettier-bytes" "^0.3.4"
    "@uppy/utils" "^6.1.1"
    compressorjs "^1.2.1"
    preact "^10.5.13"
    promise-queue "^2.2.5"

"@uppy/core@^4.4.7":
  version "4.4.7"
  resolved "https://registry.yarnpkg.com/@uppy/core/-/core-4.4.7.tgz#5a000635ee00fc162dbd1c80a5965e29de77c7af"
  integrity sha512-ZEdRiVnkHVITS7afBCWxQGNKOZ22DFXoDb4ZcLK2Srp5iBnxUbg1rV3sntHDNjZq7QHQAtZqHKAF8RxE6ZSNeg==
  dependencies:
    "@transloadit/prettier-bytes" "^0.3.4"
    "@uppy/store-default" "^4.2.0"
    "@uppy/utils" "^6.1.5"
    lodash "^4.17.21"
    mime-match "^1.0.2"
    namespace-emitter "^2.0.1"
    nanoid "^5.0.9"
    preact "^10.5.13"

"@uppy/dashboard@^4.3.4":
  version "4.3.4"
  resolved "https://registry.yarnpkg.com/@uppy/dashboard/-/dashboard-4.3.4.tgz#2b63c5424833cf311a396d4b35d6cc0dfab43c45"
  integrity sha512-SMPa5K3jZ2qNf110Hf8adN/cEAQLdpvXGjgl+R9c8AnUdpKE5f4XxaWSukdW6N7YYWmoBrLGesFvwRSPKZzCOw==
  dependencies:
    "@transloadit/prettier-bytes" "^0.3.4"
    "@uppy/informer" "^4.2.1"
    "@uppy/provider-views" "^4.4.3"
    "@uppy/status-bar" "^4.1.3"
    "@uppy/thumbnail-generator" "^4.1.1"
    "@uppy/utils" "^6.1.4"
    classnames "^2.2.6"
    lodash "^4.17.21"
    memoize-one "^6.0.0"
    nanoid "^5.0.9"
    preact "^10.5.13"
    shallow-equal "^3.0.0"

"@uppy/drag-drop@^4.1.3":
  version "4.1.3"
  resolved "https://registry.yarnpkg.com/@uppy/drag-drop/-/drag-drop-4.1.3.tgz#37b39550793bcaa0982a13792526ea27141820d7"
  integrity sha512-oxWTIfIM6v28Krl1fv3wrixHshkNMrp0vGiDO8W2qOgP2zSJCVx9KTagj3BY2tn9iyy4A7yuQZXKPmmzgfPyaQ==
  dependencies:
    "@uppy/utils" "^6.1.4"
    preact "^10.5.13"

"@uppy/drop-target@^3.1.1":
  version "3.1.1"
  resolved "https://registry.yarnpkg.com/@uppy/drop-target/-/drop-target-3.1.1.tgz#ae2eb74e66c6333aa4ffa1200cbf93b4882fa6a5"
  integrity sha512-/2jnQ3DqfcWGjgoasLBLvwJ3fozavwSXFVULenDmPUI8YPjuxmEtOu61XnZ/OLhRnZo6Qm+kltSd+YUS0P/LNA==
  dependencies:
    "@uppy/utils" "^6.1.1"

"@uppy/dropbox@^4.2.3":
  version "4.2.3"
  resolved "https://registry.yarnpkg.com/@uppy/dropbox/-/dropbox-4.2.3.tgz#d096c7d58d8feeffbd35e973ea50172baa60a5ac"
  integrity sha512-QfQ9WRFQ352bUzlVviowQ+rFJKRYc6/3lBAJPozXGuXSiv9uRS98eurKy++0KEmfgMi+FiUW7YqMBuqvJXR09g==
  dependencies:
    "@uppy/companion-client" "^4.4.2"
    "@uppy/provider-views" "^4.4.3"
    "@uppy/utils" "^6.1.4"
    preact "^10.5.13"

"@uppy/facebook@^4.2.3":
  version "4.2.3"
  resolved "https://registry.yarnpkg.com/@uppy/facebook/-/facebook-4.2.3.tgz#776769e5b53e6487f5a3bbdbdaa3af791f42ad04"
  integrity sha512-IR5cU7s7DSnYHhz62cCMEfHgEWcIGCk1cwlmN/9uWIJuZqpYBnQqJaVS91z0nfv9oJ9IF7SppoL5r8g9H/PBwQ==
  dependencies:
    "@uppy/companion-client" "^4.4.2"
    "@uppy/provider-views" "^4.4.3"
    "@uppy/utils" "^6.1.4"
    preact "^10.5.13"

"@uppy/file-input@^4.1.3":
  version "4.1.3"
  resolved "https://registry.yarnpkg.com/@uppy/file-input/-/file-input-4.1.3.tgz#a5b2578126ef96b57df12bde0ac10b1ac6a847cf"
  integrity sha512-wiZeS46c49s8NdnbTYfamfMn4WTsvMtPRTDfBD5M7CocBWRmIfKQ/nisv/1Nhy8J1a/P0eIj9Tmf518SaepN5A==
  dependencies:
    "@uppy/utils" "^6.1.4"
    preact "^10.5.13"

"@uppy/form@^4.1.1":
  version "4.1.1"
  resolved "https://registry.yarnpkg.com/@uppy/form/-/form-4.1.1.tgz#9ff79552db9825d9372df9260344eb8e1707784c"
  integrity sha512-S4GqnFOp0Q+el8iz6tTYYdcr4vw2HU5AIeZmKT+vJdYj74JnMGWCJuaAn7VN8w5Bm28bgYoK5M37cGviMB0yrw==
  dependencies:
    "@uppy/utils" "^6.1.1"
    get-form-data "^3.0.0"

"@uppy/golden-retriever@^4.1.1":
  version "4.1.1"
  resolved "https://registry.yarnpkg.com/@uppy/golden-retriever/-/golden-retriever-4.1.1.tgz#174da0da9f42db3b40fed3e9222d45d544e0bd21"
  integrity sha512-ZzgG2p0iS/4xAOVQjckIOO29otZpxJEaZr6aDvNvc67eW0VhRMqWQhq/1X4bULmKg2TVIW06vaECd71DucUsQw==
  dependencies:
    "@uppy/utils" "^6.1.1"
    lodash "^4.17.21"

"@uppy/google-drive-picker@^0.3.6":
  version "0.3.6"
  resolved "https://registry.yarnpkg.com/@uppy/google-drive-picker/-/google-drive-picker-0.3.6.tgz#8498164a15a5b76cffed22b54383101a863e33fc"
  integrity sha512-CwhlFl737UlO0q4e6UB+MuzVUDsY4H+Qh6KoirCvXEmTfov8/n0mA0yosA4VdBhRAU7iAKCxaA8czhFmAEaiCA==
  dependencies:
    "@uppy/companion-client" "^4.4.2"
    "@uppy/provider-views" "^4.4.5"
    "@uppy/utils" "^6.1.5"
    preact "^10.5.13"

"@uppy/google-drive@^4.3.3":
  version "4.3.3"
  resolved "https://registry.yarnpkg.com/@uppy/google-drive/-/google-drive-4.3.3.tgz#bd19ef31edbb236d2c9ef0e9b673dd1ce039083f"
  integrity sha512-hDDMgO2OJf5J/x35xZxvGK1ec1fgzvqpX9v1j+wb4tFbNPKeN7EocfhnpNITTGzUhMMeGfVgHmuob7rmTvZFqA==
  dependencies:
    "@uppy/companion-client" "^4.4.2"
    "@uppy/provider-views" "^4.4.3"
    "@uppy/utils" "^6.1.4"
    preact "^10.5.13"

"@uppy/google-photos-picker@^0.3.6":
  version "0.3.6"
  resolved "https://registry.yarnpkg.com/@uppy/google-photos-picker/-/google-photos-picker-0.3.6.tgz#19a39d51bd3ae20be20e99212bbc0e9f35456bdb"
  integrity sha512-kxahIcQhz81SBxwFS/j+JogEneKXtzbhwMD+SspULUh29S3duHJV5nWC03/ImoagbTTO4ys+SWpK/gNjcOAy7w==
  dependencies:
    "@uppy/companion-client" "^4.4.2"
    "@uppy/provider-views" "^4.4.5"
    "@uppy/utils" "^6.1.5"
    preact "^10.5.13"

"@uppy/image-editor@^3.3.3":
  version "3.3.3"
  resolved "https://registry.yarnpkg.com/@uppy/image-editor/-/image-editor-3.3.3.tgz#ecb32046b4e1c69463779e283b253491cbf392b6"
  integrity sha512-1jfApC1nDYdvcOsWuRA+ZqlS5CxeW9HYZE8xOXCmnDNkmNYmemro0f6MU5tZrrPw8MncH6JOvzjuffPYQL0mzg==
  dependencies:
    "@uppy/utils" "^6.1.4"
    cropperjs "^1.6.2"
    preact "^10.5.13"

"@uppy/informer@^4.2.1":
  version "4.2.1"
  resolved "https://registry.yarnpkg.com/@uppy/informer/-/informer-4.2.1.tgz#35c6d8750f1a388fea51307821d54106fdec7193"
  integrity sha512-0en8Py47pl6RMDrgUfqFoF807W5kK5AKVJNT1SkTsLiGg5anmTIMuvmNG3k6LN4cn9P/rKyEHSdGcoBBUj9u7Q==
  dependencies:
    "@uppy/utils" "^6.1.1"
    preact "^10.5.13"

"@uppy/instagram@^4.2.3":
  version "4.2.3"
  resolved "https://registry.yarnpkg.com/@uppy/instagram/-/instagram-4.2.3.tgz#fa5cb1474b91f1ebe83278920a7abd189602e849"
  integrity sha512-ohGuJHnMm1CgNVv+WzEymIbPRXjxIbGBjXbRF+laGFMLRDui7wG6L2BfUCEoCcITccmrfrfR7u5udzy0FSAghg==
  dependencies:
    "@uppy/companion-client" "^4.4.2"
    "@uppy/provider-views" "^4.4.3"
    "@uppy/utils" "^6.1.4"
    preact "^10.5.13"

"@uppy/onedrive@^4.2.4":
  version "4.2.4"
  resolved "https://registry.yarnpkg.com/@uppy/onedrive/-/onedrive-4.2.4.tgz#ea17c1aa768bf75492c8718bc685b08564f2a4ef"
  integrity sha512-JQIppfeQ2kn1Dnyy6YOcIfp8kIq+o9LCtCpL3w5GPkM2XKt82bMwrcvqRSygUi7mYVPrdQPwYda+U6mBshmavQ==
  dependencies:
    "@uppy/companion-client" "^4.4.2"
    "@uppy/provider-views" "^4.4.3"
    "@uppy/utils" "^6.1.4"
    preact "^10.5.13"

"@uppy/progress-bar@^4.2.1":
  version "4.2.1"
  resolved "https://registry.yarnpkg.com/@uppy/progress-bar/-/progress-bar-4.2.1.tgz#258f0665dc0c8c8ffda8a9fc58bed565fe40f741"
  integrity sha512-5TrUYYt1e/Qy4L4GS7pHeH9I9/zYpp7SiJzC5BtYlku5J6yxZbdxpMPW1mBhQqW+ou/IByaVIGFIR6iSq6yo0w==
  dependencies:
    "@uppy/utils" "^6.1.1"
    preact "^10.5.13"

"@uppy/provider-views@^4.4.2", "@uppy/provider-views@^4.4.3", "@uppy/provider-views@^4.4.5":
  version "4.4.5"
  resolved "https://registry.yarnpkg.com/@uppy/provider-views/-/provider-views-4.4.5.tgz#dac5651f08bd062787ded84e039b4337af97fa51"
  integrity sha512-ncPRr+morAgl/i/Rm6+Ue2O52zBtPBUNnEsixP24IxC8c9dRnG+Q/n1xNKbxY6Bd1e2X+TJs+SolxL5sPfxEfQ==
  dependencies:
    "@uppy/utils" "^6.1.5"
    classnames "^2.2.6"
    nanoid "^5.0.9"
    p-queue "^8.0.0"
    preact "^10.5.13"

"@uppy/redux-dev-tools@^4.0.1":
  version "4.0.1"
  resolved "https://registry.yarnpkg.com/@uppy/redux-dev-tools/-/redux-dev-tools-4.0.1.tgz#79ab9662ae703acf876ce25bdecd28a27447cd36"
  integrity sha512-YpMPhJqjUbI2ebyBKGx19US4UDDfcqCVebvMhQvL7BqlirjjBGykYdxuGLg7Uui7hvlAOvdsi+iwLtOeuOZHbg==

"@uppy/remote-sources@^2.3.4":
  version "2.3.4"
  resolved "https://registry.yarnpkg.com/@uppy/remote-sources/-/remote-sources-2.3.4.tgz#d67cc685addd982ab7baf4ad01b135753161a962"
  integrity sha512-oOfCQ+TAoDmQZJfeyTY87NuwL9Pl1+t4U3qncxbANsoy9PLBKjMtIvepSWmmhydc11C283O0NbOCn1kzaeg9Ng==
  dependencies:
    "@uppy/box" "^3.2.3"
    "@uppy/dashboard" "^4.3.4"
    "@uppy/dropbox" "^4.2.3"
    "@uppy/facebook" "^4.2.3"
    "@uppy/google-drive" "^4.3.3"
    "@uppy/instagram" "^4.2.3"
    "@uppy/onedrive" "^4.2.4"
    "@uppy/unsplash" "^4.3.4"
    "@uppy/url" "^4.2.4"
    "@uppy/zoom" "^3.2.3"

"@uppy/screen-capture@^4.3.1":
  version "4.3.1"
  resolved "https://registry.yarnpkg.com/@uppy/screen-capture/-/screen-capture-4.3.1.tgz#c7b3214422bc2394552b7c2eaccc2b1f90df0c83"
  integrity sha512-kaujh3xKAeUX+JdRnO5OrSKYJwBmE27orvAuF7Oo75l5/zhnEC83UaPvaP4xRVa1n0AFLbyT3wcLb5mjVw9X5g==
  dependencies:
    "@uppy/utils" "^6.1.5"
    preact "^10.5.13"

"@uppy/status-bar@^4.1.3":
  version "4.1.3"
  resolved "https://registry.yarnpkg.com/@uppy/status-bar/-/status-bar-4.1.3.tgz#770cfd3d14085047627ccd3e9655477cd2e6a3a1"
  integrity sha512-1YlbsoA9lTNL2b7nhehDri15XslVzGLG+J7HFAsxbE2cMHnOusuLCkm03oE9c72pOU9nG2qZV6yqdWBTwdxbNA==
  dependencies:
    "@transloadit/prettier-bytes" "^0.3.4"
    "@uppy/utils" "^6.1.3"
    classnames "^2.2.6"
    preact "^10.5.13"

"@uppy/store-default@^4.2.0":
  version "4.2.0"
  resolved "https://registry.yarnpkg.com/@uppy/store-default/-/store-default-4.2.0.tgz#fb91a8e76639baf0639bfd31c604838bbf2529f3"
  integrity sha512-PieFVa8yTvRHIqsNKfpO/yaJw5Ae/hT7uT58ryw7gvCBY5bHrNWxH5N0XFe8PFHMpLpLn8v3UXGx9ib9QkB6+Q==

"@uppy/store-redux@^4.0.2":
  version "4.0.2"
  resolved "https://registry.yarnpkg.com/@uppy/store-redux/-/store-redux-4.0.2.tgz#959d526fc141138c9f13940477e594e8ff0df783"
  integrity sha512-ideTWfNE1XxXHzvhiWlh/ZdRVew0uxE1AswpEYJMQEaZ9Hab7EmGS6UOryjtEfeuqsMgqQ5vReBWWGEGccBbww==
  dependencies:
    nanoid "^5.0.9"

"@uppy/thumbnail-generator@^4.1.1":
  version "4.1.1"
  resolved "https://registry.yarnpkg.com/@uppy/thumbnail-generator/-/thumbnail-generator-4.1.1.tgz#a6b902a22421d01f14313bd72fa71e634548b906"
  integrity sha512-65znkGNgVTbVte51IKOhgxOpHGSwYj9Qik2jF2ZBocMbhBY4gPkWFwqMrKQBfddA9KbUa4jVe1psxhAQTzYgiA==
  dependencies:
    "@uppy/utils" "^6.1.1"
    exifr "^7.0.0"

"@uppy/transloadit@^4.2.2":
  version "4.2.2"
  resolved "https://registry.yarnpkg.com/@uppy/transloadit/-/transloadit-4.2.2.tgz#a5a03fb8de9d0c4d54e983f0713a2397a108d8fd"
  integrity sha512-R7iKDi3gbbcZydUulSLIwCAI5krPTZBUfWIBkdc4XpcL5oGQHHjLmVbSIyLgsTBrBowrtaQP2Nj6ZkWtF6LF+Q==
  dependencies:
    "@uppy/companion-client" "^4.4.1"
    "@uppy/provider-views" "^4.4.2"
    "@uppy/tus" "^4.2.2"
    "@uppy/utils" "^6.1.3"
    component-emitter "^2.0.0"

"@uppy/tus@^4.2.2":
  version "4.2.2"
  resolved "https://registry.yarnpkg.com/@uppy/tus/-/tus-4.2.2.tgz#62bdc261ba05db1d26f227387ac12f6f8818a1dc"
  integrity sha512-fauUHqoLDtyRXwoaIyWM8ctuJ+SAXdjuM2eyoPYcGtpVaEGa+AS7IQkJkWz2RrWSdLCHL9O+fk6jKr+0PIDEpQ==
  dependencies:
    "@uppy/companion-client" "^4.4.1"
    "@uppy/utils" "^6.1.1"
    tus-js-client "^4.2.3"

"@uppy/unsplash@^4.3.4":
  version "4.3.4"
  resolved "https://registry.yarnpkg.com/@uppy/unsplash/-/unsplash-4.3.4.tgz#111ae5cfaa580d8688e72d2221371a20af7acbbc"
  integrity sha512-soLg50h4BwEw1pbx303MvfNy6BxSLOg3+Anp/WA6rR4dLYGiIb5uSB9kCLeb1kbTJe7MFJlXP1QiiIEMTVTGjA==
  dependencies:
    "@uppy/companion-client" "^4.4.2"
    "@uppy/provider-views" "^4.4.3"
    "@uppy/utils" "^6.1.4"
    preact "^10.5.13"

"@uppy/url@^4.2.4":
  version "4.2.4"
  resolved "https://registry.yarnpkg.com/@uppy/url/-/url-4.2.4.tgz#5343ce1e9af9bfcc5f43fa65addd73ef4363fb02"
  integrity sha512-EUBcRbq6EzzsfVaeQKB5eJ4TKeJYg9RNUFfOGmkSLLWwh4VGZDPzs2n4mt8MEabRsjTa8atWjWlfoCcT5YMXJQ==
  dependencies:
    "@uppy/companion-client" "^4.4.2"
    "@uppy/utils" "^6.1.4"
    nanoid "^5.0.9"
    preact "^10.5.13"

"@uppy/utils@^6.1.1", "@uppy/utils@^6.1.2", "@uppy/utils@^6.1.3", "@uppy/utils@^6.1.4", "@uppy/utils@^6.1.5":
  version "6.1.5"
  resolved "https://registry.yarnpkg.com/@uppy/utils/-/utils-6.1.5.tgz#161203d440325a9417ad1e9010a6a6c1690e6e97"
  integrity sha512-R+3l4ir01I6MzZ80t5CSBoOGvV9mCqLZC9FoNYVTp/lsanQ3yMAZFGLN/x7JVGO7oi/m/ykmNR1jAH+JTtXdFg==
  dependencies:
    lodash "^4.17.21"
    preact "^10.5.13"

"@uppy/webcam@^4.2.1":
  version "4.2.1"
  resolved "https://registry.yarnpkg.com/@uppy/webcam/-/webcam-4.2.1.tgz#2df42a7469e543580773999c37a0e06b22fda0bc"
  integrity sha512-zcQVvYlVqszZCDVoVZVssV0BwEYy3/fg9hqqusT42P2LVFmo8AKrIkK0oDFBfLimMGU5ofa5WYH6kdiWEKv+zA==
  dependencies:
    "@uppy/utils" "^6.1.5"
    is-mobile "^4.0.0"
    preact "^10.5.13"

"@uppy/webdav@^0.3.3":
  version "0.3.3"
  resolved "https://registry.yarnpkg.com/@uppy/webdav/-/webdav-0.3.3.tgz#335fd26e3d98f8d828a1c46495626e7e2d4f7bdb"
  integrity sha512-vmKCQVV1a8bfa/dMwnjWCIHU6AP9SUkt6tvO+V2IJMUZgdPgyxgpFYz9TNGKqnoBEUcWvTv7dFxVGAk85ZQuyg==
  dependencies:
    "@uppy/companion-client" "^4.4.2"
    "@uppy/provider-views" "^4.4.3"
    "@uppy/utils" "^6.1.4"
    preact "^10.5.13"

"@uppy/xhr-upload@^4.3.3":
  version "4.3.3"
  resolved "https://registry.yarnpkg.com/@uppy/xhr-upload/-/xhr-upload-4.3.3.tgz#41f69b0c8697d7df25b15c58b243a4687b50809e"
  integrity sha512-I7RVppwTvLRlVfoW5piMxcZKzWF42E6CwYFQ42d2LzizrkG4tVLQkQrTZlw85za3nhcSrX3o/d1eNx3pzLmsdw==
  dependencies:
    "@uppy/companion-client" "^4.4.1"
    "@uppy/utils" "^6.1.2"

"@uppy/zoom@^3.2.3":
  version "3.2.3"
  resolved "https://registry.yarnpkg.com/@uppy/zoom/-/zoom-3.2.3.tgz#b7895413c48af9c7bde209c573365ce12fdf0cde"
  integrity sha512-oqRL8BvLX6yQgYaluX6u21rd5PT2HsmlFvJsPDtD/+8zJ2RbJ4sPrNwJlBP0/3NY8ug7jkatEg+PzX2hNjNEgw==
  dependencies:
    "@uppy/companion-client" "^4.4.2"
    "@uppy/provider-views" "^4.4.3"
    "@uppy/utils" "^6.1.4"
    preact "^10.5.13"

"@volo/abp.aspnetcore.mvc.ui.theme.commercial@~9.2.2":
  version "9.2.2"
  resolved "https://registry.yarnpkg.com/@volo/abp.aspnetcore.mvc.ui.theme.commercial/-/abp.aspnetcore.mvc.ui.theme.commercial-9.2.2.tgz#2df9369e161721e20f8b651040ac88d9aa10be7e"
  integrity sha512-ldv3Wi9bvhnfWSjs4GXkomkIjc4vgEtkXGgZSDT2kjG2n9IVMJj64iiK1TPz6TVcHKcm8DNSDiMIgWNBkHGHyQ==
  dependencies:
    "@abp/aspnetcore.mvc.ui.theme.shared" "~9.2.2"

"@volo/abp.aspnetcore.mvc.ui.theme.leptonx@~4.2.1":
  version "4.2.2"
  resolved "https://registry.yarnpkg.com/@volo/abp.aspnetcore.mvc.ui.theme.leptonx/-/abp.aspnetcore.mvc.ui.theme.leptonx-4.2.2.tgz#43bf0e24427557a8d8a38ed4bd30b96972de2566"
  integrity sha512-6hySlwAHMTcegSqnQD3ydIk4ylAwangi3ksIz4v9qQlPom2rkOeC7kdGvXM4t/FU6QOIwtuuOhxTnIQqNQ8k2g==
  dependencies:
    "@volo/abp.aspnetcore.mvc.ui.theme.commercial" "~9.2.2"

"@volo/account@~9.2.1":
  version "9.2.2"
  resolved "https://registry.yarnpkg.com/@volo/account/-/account-9.2.2.tgz#e4e8be25841ced7320612c6cd480d73a3f32e696"
  integrity sha512-Ia/Kmmbjzr5hkLwkSH34hmM4yGX7C3NRb+Zag0nV6GAKF6O+op/uDJ4GAMeS7N0NzCZ+NHp1Xr3rInFurBu4Kg==
  dependencies:
    "@abp/cropperjs" "~9.2.2"
    "@abp/qrcode" "~9.2.2"
    "@abp/uppy" "~9.2.2"
    "@abp/zxcvbn" "~9.2.2"
    "@volo/abp.aspnetcore.mvc.ui.theme.commercial" "~9.2.2"

"@volo/audit-logging@~9.2.1":
  version "9.2.2"
  resolved "https://registry.yarnpkg.com/@volo/audit-logging/-/audit-logging-9.2.2.tgz#d4a8249ac1e03d795cf4ac6b88e46d1decf1d901"
  integrity sha512-S5jHmbzJswu9G+gRvDpNYkfiXq56hSyjL4qZXqZwVqcJHhxYrX3C4ZRIjp5Aog6MgO04IMDNAMqKZvetVgL2ig==
  dependencies:
    "@abp/chart.js" "~9.2.2"
    "@volo/abp.aspnetcore.mvc.ui.theme.commercial" "~9.2.2"

"@volo/identity@~9.2.1":
  version "9.2.2"
  resolved "https://registry.yarnpkg.com/@volo/identity/-/identity-9.2.2.tgz#5d21f0126509132a5da8ca35a0df7f18f483bf58"
  integrity sha512-ElnOEBx/8q04RrLe1Isc2pc0KpXG3JU6hhIhjMf9RjP5W5R7OJ32VkAuwP625E9yxnIdwN+YHMaq5Ixq7eQc4w==
  dependencies:
    "@abp/jstree" "~9.2.2"
    "@abp/uppy" "~9.2.2"
    "@volo/abp.aspnetcore.mvc.ui.theme.commercial" "~9.2.2"

"@volo/language-management@~9.2.1":
  version "9.2.2"
  resolved "https://registry.yarnpkg.com/@volo/language-management/-/language-management-9.2.2.tgz#b4dfeb45912a61e729ed347240149c8588f5d50b"
  integrity sha512-99XucZ6KtCNX0QC6SKpHAP3y3gbpaY2LCaeGDB9T7YRb8bJYUSvusm3kAt1iXFEyIE8XInUeoFQ0A6fKtIm5BA==
  dependencies:
    "@abp/flag-icon-css" "~9.2.2"
    "@volo/abp.aspnetcore.mvc.ui.theme.commercial" "~9.2.2"

ansi-colors@^4.1.3:
  version "4.1.3"
  resolved "https://registry.yarnpkg.com/ansi-colors/-/ansi-colors-4.1.3.tgz#37611340eb2243e70cc604cad35d63270d48781b"
  integrity sha512-/6w/C21Pm1A7aZitlI5Ni/2J6FFQN8i1Cvz3kHABAAbw93v/NlvKdVOqz7CCWz/3iv/JplRSEEZ83XION15ovw==

blueimp-canvas-to-blob@^3.29.0:
  version "3.29.0"
  resolved "https://registry.yarnpkg.com/blueimp-canvas-to-blob/-/blueimp-canvas-to-blob-3.29.0.tgz#d965f06cb1a67fdae207a2be56683f55ef531466"
  integrity sha512-0pcSSGxC0QxT+yVkivxIqW0Y4VlO2XSDPofBAqoJ1qJxgH9eiUDLv50Rixij2cDuEfx4M6DpD9UGZpRhT5Q8qg==

bootstrap-datepicker@^1.10.0:
  version "1.10.0"
  resolved "https://registry.yarnpkg.com/bootstrap-datepicker/-/bootstrap-datepicker-1.10.0.tgz#61612bbe8bf0a69a5bce32bbcdda93ebb6ccf24a"
  integrity sha512-lWxtSYddAQOpbAO8UhYhHLcK6425eWoSjb5JDvZU3ePHEPF6A3eUr51WKaFy4PccU19JRxUG6wEU3KdhtKfvpg==
  dependencies:
    jquery ">=3.4.0 <4.0.0"

bootstrap-daterangepicker@^3.1.0:
  version "3.1.0"
  resolved "https://registry.yarnpkg.com/bootstrap-daterangepicker/-/bootstrap-daterangepicker-3.1.0.tgz#632e6fb2de4b6360c5c0a9d5f6adb9aace051fe8"
  integrity sha512-oaQZx6ZBDo/dZNyXGVi2rx5GmFXThyQLAxdtIqjtLlYVaQUfQALl5JZMJJZzyDIX7blfy4ppZPAJ10g8Ma4d/g==
  dependencies:
    jquery ">=1.10"
    moment "^2.9.0"

bootstrap@^5.3.3:
  version "5.3.7"
  resolved "https://registry.yarnpkg.com/bootstrap/-/bootstrap-5.3.7.tgz#8640065036124d961d885d80b5945745e1154d90"
  integrity sha512-7KgiD8UHjfcPBHEpDNg+zGz8L3LqR3GVwqZiBRFX04a1BCArZOz1r2kjly2HQ0WokqTO0v1nF+QAt8dsW4lKlw==

buffer-from@^1.1.2:
  version "1.1.2"
  resolved "https://registry.yarnpkg.com/buffer-from/-/buffer-from-1.1.2.tgz#2b146a6fd72e80b4f55d255f35ed59a3a9a41bd5"
  integrity sha512-E+XQCRwSbaaiChtv6k6Dwgc+bx+Bs6vuKJHHl5kox/BaKbhiXzqQOwK4cO22yElGp2OCmjwVhT3HmxgyPGnJfQ==

chart.js@^4.4.4:
  version "4.5.0"
  resolved "https://registry.yarnpkg.com/chart.js/-/chart.js-4.5.0.tgz#11a1ef6c4befc514b1b0b613ebac226c4ad2740b"
  integrity sha512-aYeC/jDgSEx8SHWZvANYMioYMZ2KX02W6f6uVfyteuCGcadDLcYVHdfdygsTQkQ4TKn5lghoojAsPj5pu0SnvQ==
  dependencies:
    "@kurkle/color" "^0.3.0"

classnames@^2.2.6:
  version "2.5.1"
  resolved "https://registry.yarnpkg.com/classnames/-/classnames-2.5.1.tgz#ba774c614be0f016da105c858e7159eae8e7687b"
  integrity sha512-saHYOzhIQs6wy2sVxTM6bUDsQO4F50V9RQ22qBpEdCW+I+/Wmke2HOl6lS6dTpdxVhb88/I6+Hs+438c3lfUow==

combine-errors@^3.0.3:
  version "3.0.3"
  resolved "https://registry.yarnpkg.com/combine-errors/-/combine-errors-3.0.3.tgz#f4df6740083e5703a3181110c2b10551f003da86"
  integrity sha512-C8ikRNRMygCwaTx+Ek3Yr+OuZzgZjduCOfSQBjbM8V3MfgcjSTeto/GXP6PAwKvJz/v15b7GHZvx5rOlczFw/Q==
  dependencies:
    custom-error-instance "2.1.1"
    lodash.uniqby "4.5.0"

component-emitter@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/component-emitter/-/component-emitter-2.0.0.tgz#3a137dfe66fcf2efe3eab7cb7d5f51741b3620c6"
  integrity sha512-4m5s3Me2xxlVKG9PkZpQqHQR7bgpnN7joDMJ4yvVkVXngjoITG76IaZmzmywSeRTeTpc6N6r3H3+KyUurV8OYw==

compressorjs@^1.2.1:
  version "1.2.1"
  resolved "https://registry.yarnpkg.com/compressorjs/-/compressorjs-1.2.1.tgz#4dee18ef5032f8166bd0a3258f045eda2cd07671"
  integrity sha512-+geIjeRnPhQ+LLvvA7wxBQE5ddeLU7pJ3FsKFWirDw6veY3s9iLxAQEw7lXGHnhCJvBujEQWuNnGzZcvCvdkLQ==
  dependencies:
    blueimp-canvas-to-blob "^3.29.0"
    is-blob "^2.1.0"

cropperjs@^1.6.2:
  version "1.6.2"
  resolved "https://registry.yarnpkg.com/cropperjs/-/cropperjs-1.6.2.tgz#d1a5d627d880581cca41b7901f06923500e4201b"
  integrity sha512-nhymn9GdnV3CqiEHJVai54TULFAE3VshJTXSqSJKa8yXAKyBKDWdhHarnlIPrshJ0WMFTGuFvG02YjLXfPiuOA==

custom-error-instance@2.1.1:
  version "2.1.1"
  resolved "https://registry.yarnpkg.com/custom-error-instance/-/custom-error-instance-2.1.1.tgz#3cf6391487a6629a6247eb0ca0ce00081b7e361a"
  integrity sha512-p6JFxJc3M4OTD2li2qaHkDCw9SfMw82Ldr6OC9Je1aXiGfhx2W8p3GaoeaGrPJTUN9NirTM/KTxHWMUdR1rsUg==

datatables.net-bs5@^2.1.8:
  version "2.3.2"
  resolved "https://registry.yarnpkg.com/datatables.net-bs5/-/datatables.net-bs5-2.3.2.tgz#cffb8007a9f752a997bc70c0dbe9f545edfd18eb"
  integrity sha512-1rh0ZTLoiziIQ4oAtgr+IOYVgJfAIceDnbDe535u8kv191pBAdTrKF6ovQO98Xy9mDXLdLNB7QCrLiV/sgPoQw==
  dependencies:
    datatables.net "2.3.2"
    jquery ">=1.7"

datatables.net@2.3.2, datatables.net@^2.1.8:
  version "2.3.2"
  resolved "https://registry.yarnpkg.com/datatables.net/-/datatables.net-2.3.2.tgz#6821f6288e6ad3cb6879c33e0e7e11d4091d330b"
  integrity sha512-31TzwIQM0+pr2ZOEOEH6dsHd/WSAl5GDDGPezOHPI3mM2NK4lcDyOoG8xXeWmSbVfbi852LNK5C84fpp4Q+qxg==
  dependencies:
    jquery ">=1.7"

eventemitter3@^5.0.1:
  version "5.0.1"
  resolved "https://registry.yarnpkg.com/eventemitter3/-/eventemitter3-5.0.1.tgz#53f5ffd0a492ac800721bb42c66b841de96423c4"
  integrity sha512-GWkBvjiSZK87ELrYOSESUYeVIc9mvLLf/nXalMOS5dYrgZq9o5OVkbZAVM06CVxYsCwH9BDZFPlQTlPA1j4ahA==

exifr@^7.0.0:
  version "7.1.3"
  resolved "https://registry.yarnpkg.com/exifr/-/exifr-7.1.3.tgz#f6218012c36dbb7d843222011b27f065fddbab6f"
  integrity sha512-g/aje2noHivrRSLbAUtBPWFbxKdKhgj/xr1vATDdUXPOFYJlQ62Ft0oy+72V6XLIpDJfHs6gXLbBLAolqOXYRw==

flag-icon-css@^4.1.7:
  version "4.1.7"
  resolved "https://registry.yarnpkg.com/flag-icon-css/-/flag-icon-css-4.1.7.tgz#5471197f9ab965a3603b3e0face31dd513fec289"
  integrity sha512-AFjSU+fv98XbU0vnTQ32vcLj89UEr1MhwDFcooQv14qWJCjg9fGZzfh9BVyDhAhIOZW/pGmJmq38RqpgPaeybQ==

get-form-data@^3.0.0:
  version "3.0.0"
  resolved "https://registry.yarnpkg.com/get-form-data/-/get-form-data-3.0.0.tgz#7abbf0e75e5ff155f75ba26eadeb9a4d70bf95dc"
  integrity sha512-1d53Kn08wlPuLu31/boF1tW2WRYKw3xAWae3mqcjqpDjoqVBtXolbQnudbbEFyFWL7+2SLGRAFdotxNY06V7MA==

graceful-fs@^4.2.4:
  version "4.2.11"
  resolved "https://registry.yarnpkg.com/graceful-fs/-/graceful-fs-4.2.11.tgz#4183e4e8bf08bb6e05bbb2f7d2e0c8f712ca40e3"
  integrity sha512-RbJ5/jmFcNNCcDV5o9eTnBLJ/HszWV0P73bc+Ff4nS/rJj+YaS6IGyiOL0VoBYX+l1Wrl3k63h/KrH+nhJ0XvQ==

is-blob@^2.1.0:
  version "2.1.0"
  resolved "https://registry.yarnpkg.com/is-blob/-/is-blob-2.1.0.tgz#e36cd82c90653f1e1b930f11baf9c64216a05385"
  integrity sha512-SZ/fTft5eUhQM6oF/ZaASFDEdbFVe89Imltn9uZr03wdKMcWNVYSMjQPFtg05QuNkt5l5c135ElvXEQG0rk4tw==

is-mobile@^4.0.0:
  version "4.0.0"
  resolved "https://registry.yarnpkg.com/is-mobile/-/is-mobile-4.0.0.tgz#bba396eb9656e2739afde3053d7191da310fc758"
  integrity sha512-mlcHZA84t1qLSuWkt2v0I2l61PYdyQDt4aG1mLIXF5FDMm4+haBCxCPYSr/uwqQNRk1MiTizn0ypEuRAOLRAew==

is-network-error@^1.0.0:
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/is-network-error/-/is-network-error-1.1.0.tgz#d26a760e3770226d11c169052f266a4803d9c997"
  integrity sha512-tUdRRAnhT+OtCZR/LxZelH/C7QtjtFrTu5tXCA8pl55eTUElUHT+GPYV8MBMBvea/j+NxQqVt3LbWMRir7Gx9g==

is-stream@^2.0.0:
  version "2.0.1"
  resolved "https://registry.yarnpkg.com/is-stream/-/is-stream-2.0.1.tgz#fac1e3d53b97ad5a9d0ae9cef2389f5810a5c077"
  integrity sha512-hFoiJiTl63nn+kstHGBtewWSKnQLpyb155KHheA1l39uvtO9nWIop1p3udqPcUd/xbF1VLMO4n7OI6p7RbngDg==

jquery-form@^4.3.0:
  version "4.3.0"
  resolved "https://registry.yarnpkg.com/jquery-form/-/jquery-form-4.3.0.tgz#7d3961c314a1f2d15298f4af1d3943f54f4149c6"
  integrity sha512-q3uaVCEWdLOYUCI6dpNdwf/7cJFOsUgdpq6r0taxtGQ5NJSkOzofyWm4jpOuJ5YxdmL1FI5QR+q+HB63HHLGnQ==
  dependencies:
    jquery ">=1.7.2"

jquery-mousewheel@>=3.0.6:
  version "3.2.2"
  resolved "https://registry.yarnpkg.com/jquery-mousewheel/-/jquery-mousewheel-3.2.2.tgz#48c833f6260ee0c46d438a999e7d0060ec9eed0b"
  integrity sha512-JP71xTAg08ZY3hcs9ZbYUZ5i+dkSsz4yRl/zpWkAmtzc+kMs5EfPkpkINSidiLYMaR0MTo3DfFGF9WIezMsFQQ==
  dependencies:
    jquery ">=1.2.6"

jquery-validation-unobtrusive@^4.0.0:
  version "4.0.0"
  resolved "https://registry.yarnpkg.com/jquery-validation-unobtrusive/-/jquery-validation-unobtrusive-4.0.0.tgz#dfcf25a558496a2c883db6021d10f5398d15f99d"
  integrity sha512-1ervYFFv6LX/rp7ktuLnMakHNG0piNRDyROI8Ir3hL1vPIwylAehB1AY3BPrYJnzW3WmwWryZq+Bz4sazZK9iQ==
  dependencies:
    jquery "^3.6.0"
    jquery-validation ">=1.19"

jquery-validation@>=1.19, jquery-validation@^1.21.0:
  version "1.21.0"
  resolved "https://registry.yarnpkg.com/jquery-validation/-/jquery-validation-1.21.0.tgz#78fc05ab76020912a246af3661b3f54a438bca93"
  integrity sha512-xNot0rlUIgu7duMcQ5qb6MGkGL/Z1PQaRJQoZAURW9+a/2PGOUxY36o/WyNeP2T9R6jvWB8Z9lUVvvQWI/Zs5w==

jquery@>=1.10, jquery@>=1.2.6, "jquery@>=1.5.0 <4.0", jquery@>=1.7, jquery@>=1.7.2, "jquery@>=3.4.0 <4.0.0", jquery@^3.5.0, jquery@^3.6.0, jquery@~3.7.1:
  version "3.7.1"
  resolved "https://registry.yarnpkg.com/jquery/-/jquery-3.7.1.tgz#083ef98927c9a6a74d05a6af02806566d16274de"
  integrity sha512-m4avr8yL8kmFN8psrbFFFmB/If14iN5o9nw/NgnnM+kybDJpRsAynV2BsfpTYrTRysYUdADVD7CkUUizgkpLfg==

js-base64@^3.7.2:
  version "3.7.7"
  resolved "https://registry.yarnpkg.com/js-base64/-/js-base64-3.7.7.tgz#e51b84bf78fbf5702b9541e2cb7bfcb893b43e79"
  integrity sha512-7rCnleh0z2CkXhH67J8K1Ytz0b2Y+yxTPL+/KOJoa20hfnVQ/3/T6W/KflYI4bRHRagNeXeU2bkNGI3v1oS/lw==

jstree@^3.3.17:
  version "3.3.17"
  resolved "https://registry.yarnpkg.com/jstree/-/jstree-3.3.17.tgz#aa7f22ee504ba7f63f8942c3d71c62ff21f756cf"
  integrity sha512-V0Pl1B6BIaxHUQMiWkol37QlhlBKoZA9RUeUmm95+UnV2QeZsj2QP1sOQl/m2EIOyZXOOxOHvR0SYZLSDC7EFw==
  dependencies:
    jquery "^3.5.0"

just-compare@^2.3.0:
  version "2.3.0"
  resolved "https://registry.yarnpkg.com/just-compare/-/just-compare-2.3.0.tgz#a2adcc1d1940536263275f5a1ef1298bcacfeda7"
  integrity sha512-6shoR7HDT+fzfL3gBahx1jZG3hWLrhPAf+l7nCwahDdT9XDtosB9kIF0ZrzUp5QY8dJWfQVr5rnsPqsbvflDzg==

lodash._baseiteratee@~4.7.0:
  version "4.7.0"
  resolved "https://registry.yarnpkg.com/lodash._baseiteratee/-/lodash._baseiteratee-4.7.0.tgz#34a9b5543572727c3db2e78edae3c0e9e66bd102"
  integrity sha512-nqB9M+wITz0BX/Q2xg6fQ8mLkyfF7MU7eE+MNBNjTHFKeKaZAPEzEg+E8LWxKWf1DQVflNEn9N49yAuqKh2mWQ==
  dependencies:
    lodash._stringtopath "~4.8.0"

lodash._basetostring@~4.12.0:
  version "4.12.0"
  resolved "https://registry.yarnpkg.com/lodash._basetostring/-/lodash._basetostring-4.12.0.tgz#9327c9dc5158866b7fa4b9d42f4638e5766dd9df"
  integrity sha512-SwcRIbyxnN6CFEEK4K1y+zuApvWdpQdBHM/swxP962s8HIxPO3alBH5t3m/dl+f4CMUug6sJb7Pww8d13/9WSw==

lodash._baseuniq@~4.6.0:
  version "4.6.0"
  resolved "https://registry.yarnpkg.com/lodash._baseuniq/-/lodash._baseuniq-4.6.0.tgz#0ebb44e456814af7905c6212fa2c9b2d51b841e8"
  integrity sha512-Ja1YevpHZctlI5beLA7oc5KNDhGcPixFhcqSiORHNsp/1QTv7amAXzw+gu4YOvErqVlMVyIJGgtzeepCnnur0A==
  dependencies:
    lodash._createset "~4.0.0"
    lodash._root "~3.0.0"

lodash._createset@~4.0.0:
  version "4.0.3"
  resolved "https://registry.yarnpkg.com/lodash._createset/-/lodash._createset-4.0.3.tgz#0f4659fbb09d75194fa9e2b88a6644d363c9fe26"
  integrity sha512-GTkC6YMprrJZCYU3zcqZj+jkXkrXzq3IPBcF/fIPpNEAB4hZEtXU8zp/RwKOvZl43NUmwDbyRk3+ZTbeRdEBXA==

lodash._root@~3.0.0:
  version "3.0.1"
  resolved "https://registry.yarnpkg.com/lodash._root/-/lodash._root-3.0.1.tgz#fba1c4524c19ee9a5f8136b4609f017cf4ded692"
  integrity sha512-O0pWuFSK6x4EXhM1dhZ8gchNtG7JMqBtrHdoUFUWXD7dJnNSUze1GuyQr5sOs0aCvgGeI3o/OJW8f4ca7FDxmQ==

lodash._stringtopath@~4.8.0:
  version "4.8.0"
  resolved "https://registry.yarnpkg.com/lodash._stringtopath/-/lodash._stringtopath-4.8.0.tgz#941bcf0e64266e5fc1d66fed0a6959544c576824"
  integrity sha512-SXL66C731p0xPDC5LZg4wI5H+dJo/EO4KTqOMwLYCH3+FmmfAKJEZCm6ohGpI+T1xwsDsJCfL4OnhorllvlTPQ==
  dependencies:
    lodash._basetostring "~4.12.0"

lodash.throttle@^4.1.1:
  version "4.1.1"
  resolved "https://registry.yarnpkg.com/lodash.throttle/-/lodash.throttle-4.1.1.tgz#c23e91b710242ac70c37f1e1cda9274cc39bf2f4"
  integrity sha512-wIkUCfVKpVsWo3JSZlc+8MB5it+2AN5W8J7YVMST30UrvcQNZ1Okbj+rbVniijTWE6FGYy4XJq/rHkas8qJMLQ==

lodash.uniqby@4.5.0:
  version "4.5.0"
  resolved "https://registry.yarnpkg.com/lodash.uniqby/-/lodash.uniqby-4.5.0.tgz#a3a17bbf62eeb6240f491846e97c1c4e2a5e1e21"
  integrity sha512-IRt7cfTtHy6f1aRVA5n7kT8rgN3N1nH6MOWLcHfpWG2SH19E3JksLK38MktLxZDhlAjCP9jpIXkOnRXlu6oByQ==
  dependencies:
    lodash._baseiteratee "~4.7.0"
    lodash._baseuniq "~4.6.0"

lodash@^4.17.21:
  version "4.17.21"
  resolved "https://registry.yarnpkg.com/lodash/-/lodash-4.17.21.tgz#679591c564c3bffaae8454cf0b3df370c3d6911c"
  integrity sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==

luxon@^3.5.0:
  version "3.7.1"
  resolved "https://registry.yarnpkg.com/luxon/-/luxon-3.7.1.tgz#9bd09aa84a56afb00c57ea78a8ec5bd16eb24ec0"
  integrity sha512-RkRWjA926cTvz5rAb1BqyWkKbbjzCGchDUIKMCUvNi17j6f6j8uHGDV82Aqcqtzd+icoYpELmG3ksgGiFNNcNg==

malihu-custom-scrollbar-plugin@^3.1.5:
  version "3.1.5"
  resolved "https://registry.yarnpkg.com/malihu-custom-scrollbar-plugin/-/malihu-custom-scrollbar-plugin-3.1.5.tgz#310cecc5e59415a1c29e9dfb5d2b6e01d66a29ef"
  integrity sha512-lwW3LgI+CNDMPnP4ED2la6oYxWMkCXlnhex+s2wuOLhFDFGnGmQuTQVdRK9bvDLpxs10sGlfErVufJy9ztfgJQ==
  dependencies:
    jquery-mousewheel ">=3.0.6"

memoize-one@^6.0.0:
  version "6.0.0"
  resolved "https://registry.yarnpkg.com/memoize-one/-/memoize-one-6.0.0.tgz#b2591b871ed82948aee4727dc6abceeeac8c1045"
  integrity sha512-rkpe71W0N0c0Xz6QD0eJETuWAJGnJ9afsl1srmwPrI+yBCkge5EycXXbYRyvL29zZVUWQCY7InPRCv3GDXuZNw==

mime-match@^1.0.2:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/mime-match/-/mime-match-1.0.2.tgz#3f87c31e9af1a5fd485fb9db134428b23bbb7ba8"
  integrity sha512-VXp/ugGDVh3eCLOBCiHZMYWQaTNUHv2IJrut+yXA6+JbLPXHglHwfS/5A5L0ll+jkCY7fIzRJcH6OIunF+c6Cg==
  dependencies:
    wildcard "^1.1.0"

moment@^2.30.1, moment@^2.9.0:
  version "2.30.1"
  resolved "https://registry.yarnpkg.com/moment/-/moment-2.30.1.tgz#f8c91c07b7a786e30c59926df530b4eac96974ae"
  integrity sha512-uEmtNhbDOrWPFS+hdjFCBfy9f2YoyzRpwcl+DqpC6taX21FzsTLQVbMV/W7PzNSX6x/bhC1zA3c2UQ5NzH6how==

namespace-emitter@^2.0.1:
  version "2.0.1"
  resolved "https://registry.yarnpkg.com/namespace-emitter/-/namespace-emitter-2.0.1.tgz#978d51361c61313b4e6b8cf6f3853d08dfa2b17c"
  integrity sha512-N/sMKHniSDJBjfrkbS/tpkPj4RAbvW3mr8UAzvlMHyun93XEm83IAvhWtJVHo+RHn/oO8Job5YN4b+wRjSVp5g==

nanoid@^5.0.9:
  version "5.1.5"
  resolved "https://registry.yarnpkg.com/nanoid/-/nanoid-5.1.5.tgz#f7597f9d9054eb4da9548cdd53ca70f1790e87de"
  integrity sha512-Ir/+ZpE9fDsNH0hQ3C68uyThDXzYcim2EqcZ8zn8Chtt1iylPT9xXJB0kPCnqzgcEGikO9RxSrh63MsmVCU7Fw==

p-queue@^8.0.0:
  version "8.1.0"
  resolved "https://registry.yarnpkg.com/p-queue/-/p-queue-8.1.0.tgz#d71929249868b10b16f885d8a82beeaf35d32279"
  integrity sha512-mxLDbbGIBEXTJL0zEx8JIylaj3xQ7Z/7eEVjcF9fJX4DBiH9oqe+oahYnlKKxm0Ci9TlWTyhSHgygxMxjIB2jw==
  dependencies:
    eventemitter3 "^5.0.1"
    p-timeout "^6.1.2"

p-retry@^6.1.0:
  version "6.2.1"
  resolved "https://registry.yarnpkg.com/p-retry/-/p-retry-6.2.1.tgz#81828f8dc61c6ef5a800585491572cc9892703af"
  integrity sha512-hEt02O4hUct5wtwg4H4KcWgDdm+l1bOaEy/hWzd8xtXB9BqxTWBBhb+2ImAtH4Cv4rPjV76xN3Zumqk3k3AhhQ==
  dependencies:
    "@types/retry" "0.12.2"
    is-network-error "^1.0.0"
    retry "^0.13.1"

p-timeout@^6.1.2:
  version "6.1.4"
  resolved "https://registry.yarnpkg.com/p-timeout/-/p-timeout-6.1.4.tgz#418e1f4dd833fa96a2e3f532547dd2abdb08dbc2"
  integrity sha512-MyIV3ZA/PmyBN/ud8vV9XzwTrNtR4jFrObymZYnZqMmW0zA8Z17vnT0rBgFE/TlohB+YCHqXMgZzb3Csp49vqg==

preact@^10.5.13:
  version "10.26.9"
  resolved "https://registry.yarnpkg.com/preact/-/preact-10.26.9.tgz#b3898d1b65140640799062ad73b89846c293b6a7"
  integrity sha512-SSjF9vcnF27mJK1XyFMNJzFd5u3pQiATFqoaDy03XuN00u4ziveVVEGt5RKJrDR8MHE/wJo9Nnad56RLzS2RMA==

promise-queue@^2.2.5:
  version "2.2.5"
  resolved "https://registry.yarnpkg.com/promise-queue/-/promise-queue-2.2.5.tgz#2f6f5f7c0f6d08109e967659c79b88a9ed5e93b4"
  integrity sha512-p/iXrPSVfnqPft24ZdNNLECw/UrtLTpT3jpAAMzl/o5/rDsGCPo3/CQS2611flL6LkoEJ3oQZw7C8Q80ZISXRQ==

proper-lockfile@^4.1.2:
  version "4.1.2"
  resolved "https://registry.yarnpkg.com/proper-lockfile/-/proper-lockfile-4.1.2.tgz#c8b9de2af6b2f1601067f98e01ac66baa223141f"
  integrity sha512-TjNPblN4BwAWMXU8s9AEz4JmQxnD1NNL7bNOY/AKUzyamc379FWASUhc/K1pL2noVb+XmZKLL68cjzLsiOAMaA==
  dependencies:
    graceful-fs "^4.2.4"
    retry "^0.12.0"
    signal-exit "^3.0.2"

querystringify@^2.1.1:
  version "2.2.0"
  resolved "https://registry.yarnpkg.com/querystringify/-/querystringify-2.2.0.tgz#3345941b4153cb9d082d8eee4cda2016a9aef7f6"
  integrity sha512-FIqgj2EUvTa7R50u0rGsyTftzjYmv/a3hO345bZNrqabNqjtgiDMgmo4mkUjd+nzU5oF3dClKqFIPUKybUyqoQ==

requires-port@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/requires-port/-/requires-port-1.0.0.tgz#925d2601d39ac485e091cf0da5c6e694dc3dcaff"
  integrity sha512-KigOCHcocU3XODJxsu8i/j8T9tzT4adHiecwORRQ0ZZFcp7ahwXuRU1m+yuO90C5ZUyGeGfocHDI14M3L3yDAQ==

retry@^0.12.0:
  version "0.12.0"
  resolved "https://registry.yarnpkg.com/retry/-/retry-0.12.0.tgz#1b42a6266a21f07421d1b0b54b7dc167b01c013b"
  integrity sha512-9LkiTwjUh6rT555DtE9rTX+BKByPfrMzEAtnlEtdEwr3Nkffwiihqe2bWADg+OQRjt9gl6ICdmB/ZFDCGAtSow==

retry@^0.13.1:
  version "0.13.1"
  resolved "https://registry.yarnpkg.com/retry/-/retry-0.13.1.tgz#185b1587acf67919d63b357349e03537b2484658"
  integrity sha512-XQBQ3I8W1Cge0Seh+6gjj03LbmRFWuoszgK9ooCpwYIrhhoO80pfq4cUkU5DkknwfOfFteRwlZ56PYOGYyFWdg==

select2@^4.0.13:
  version "4.0.13"
  resolved "https://registry.yarnpkg.com/select2/-/select2-4.0.13.tgz#0dbe377df3f96167c4c1626033e924372d8ef44d"
  integrity sha512-1JeB87s6oN/TDxQQYCvS5EFoQyvV6eYMZZ0AeA4tdFDYWN3BAGZ8npr17UBFddU0lgAt3H0yjX3X6/ekOj1yjw==

shallow-equal@^3.0.0:
  version "3.1.0"
  resolved "https://registry.yarnpkg.com/shallow-equal/-/shallow-equal-3.1.0.tgz#e7a54bac629c7f248eff6c2f5b63122ba4320bec"
  integrity sha512-pfVOw8QZIXpMbhBWvzBISicvToTiM5WBF1EeAUZDDSb5Dt29yl4AYbyywbJFSEsRUMr7gJaxqCdr4L3tQf9wVg==

signal-exit@^3.0.2:
  version "3.0.7"
  resolved "https://registry.yarnpkg.com/signal-exit/-/signal-exit-3.0.7.tgz#a9a1767f8af84155114eaabd73f99273c8f59ad9"
  integrity sha512-wnD2ZE+l+SPC/uoS0vXeE9L1+0wuaMqKlfz9AMUo38JsyLSBWSFcHR1Rri62LZc12vLr1gb3jl7iwQhgwpAbGQ==

sweetalert2@^11.14.1:
  version "11.22.2"
  resolved "https://registry.yarnpkg.com/sweetalert2/-/sweetalert2-11.22.2.tgz#d4d82a2edd4e97024306fe37f1bc64fa576e9bc9"
  integrity sha512-GFQGzw8ZXF23PO79WMAYXLl4zYmLiaKqYJwcp5eBF07wiI5BYPbZtKi2pcvVmfUQK+FqL1risJAMxugcPbGIyg==

timeago@^1.6.7:
  version "1.6.7"
  resolved "https://registry.yarnpkg.com/timeago/-/timeago-1.6.7.tgz#afd467c29a911e697fc22a81888c7c3022783cb5"
  integrity sha512-FikcjN98+ij0siKH4VO4dZ358PR3oDDq4Vdl1+sN9gWz1/+JXGr3uZbUShYH/hL7bMhcTpPbplJU5Tej4b4jbQ==
  dependencies:
    jquery ">=1.5.0 <4.0"

tus-js-client@^4.2.3:
  version "4.3.1"
  resolved "https://registry.yarnpkg.com/tus-js-client/-/tus-js-client-4.3.1.tgz#2f9a47fba006206fb0d08c649fa01254944d5d87"
  integrity sha512-ZLeYmjrkaU1fUsKbIi8JML52uAocjEZtBx4DKjRrqzrZa0O4MYwT6db+oqePlspV+FxXJAyFBc/L5gwUi2OFsg==
  dependencies:
    buffer-from "^1.1.2"
    combine-errors "^3.0.3"
    is-stream "^2.0.0"
    js-base64 "^3.7.2"
    lodash.throttle "^4.1.1"
    proper-lockfile "^4.1.2"
    url-parse "^1.5.7"

uppy@^4.4.1:
  version "4.18.0"
  resolved "https://registry.yarnpkg.com/uppy/-/uppy-4.18.0.tgz#47af2fdf4e56c2c5d72dbeb3ebd47341f8d1ecc5"
  integrity sha512-sDTdNOj+i6P9wDU+x06z0l8iBihKcrAo5Jos3Y24RMwqqRk3k8NdPH2sYunm6kR8QywBDtPu2ty91WdnnDwJKg==
  dependencies:
    "@uppy/audio" "^2.1.3"
    "@uppy/aws-s3" "^4.2.3"
    "@uppy/box" "^3.2.3"
    "@uppy/companion-client" "^4.4.2"
    "@uppy/compressor" "^2.2.1"
    "@uppy/core" "^4.4.7"
    "@uppy/dashboard" "^4.3.4"
    "@uppy/drag-drop" "^4.1.3"
    "@uppy/drop-target" "^3.1.1"
    "@uppy/dropbox" "^4.2.3"
    "@uppy/facebook" "^4.2.3"
    "@uppy/file-input" "^4.1.3"
    "@uppy/form" "^4.1.1"
    "@uppy/golden-retriever" "^4.1.1"
    "@uppy/google-drive" "^4.3.3"
    "@uppy/google-drive-picker" "^0.3.6"
    "@uppy/google-photos-picker" "^0.3.6"
    "@uppy/image-editor" "^3.3.3"
    "@uppy/informer" "^4.2.1"
    "@uppy/instagram" "^4.2.3"
    "@uppy/onedrive" "^4.2.4"
    "@uppy/progress-bar" "^4.2.1"
    "@uppy/provider-views" "^4.4.5"
    "@uppy/redux-dev-tools" "^4.0.1"
    "@uppy/remote-sources" "^2.3.4"
    "@uppy/screen-capture" "^4.3.1"
    "@uppy/status-bar" "^4.1.3"
    "@uppy/store-default" "^4.2.0"
    "@uppy/store-redux" "^4.0.2"
    "@uppy/thumbnail-generator" "^4.1.1"
    "@uppy/transloadit" "^4.2.2"
    "@uppy/tus" "^4.2.2"
    "@uppy/unsplash" "^4.3.4"
    "@uppy/url" "^4.2.4"
    "@uppy/webcam" "^4.2.1"
    "@uppy/webdav" "^0.3.3"
    "@uppy/xhr-upload" "^4.3.3"
    "@uppy/zoom" "^3.2.3"

url-parse@^1.5.7:
  version "1.5.10"
  resolved "https://registry.yarnpkg.com/url-parse/-/url-parse-1.5.10.tgz#9d3c2f736c1d75dd3bd2be507dcc111f1e2ea9c1"
  integrity sha512-WypcfiRhfeUP9vvF0j6rw0J3hrWrw6iZv3+22h6iRMJ/8z1Tj6XfLP4DsUix5MhMPnXpiHDoKyoZ/bdCkwBCiQ==
  dependencies:
    querystringify "^2.1.1"
    requires-port "^1.0.0"

wildcard@^1.1.0:
  version "1.1.2"
  resolved "https://registry.yarnpkg.com/wildcard/-/wildcard-1.1.2.tgz#a7020453084d8cd2efe70ba9d3696263de1710a5"
  integrity sha512-DXukZJxpHA8LuotRwL0pP1+rS6CS7FF2qStDDE1C7DDg2rLud2PXRMuEDYIPhgEezwnlHNL4c+N6MfMTjCGTng==

zxcvbn@^4.4.2:
  version "4.4.2"
  resolved "https://registry.yarnpkg.com/zxcvbn/-/zxcvbn-4.4.2.tgz#28ec17cf09743edcab056ddd8b1b06262cc73c30"
  integrity sha512-Bq0B+ixT/DMyG8kgX2xWcI5jUvCwqrMxSFam7m0lAf78nf04hv6lNCsyLYdyYTrCVMqNDY/206K7eExYCeSyUQ==
