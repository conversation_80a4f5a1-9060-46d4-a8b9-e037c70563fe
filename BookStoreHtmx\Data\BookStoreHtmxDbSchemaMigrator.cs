﻿using Volo.Abp.DependencyInjection;
using Microsoft.EntityFrameworkCore;

namespace BookStoreHtmx.Data;

public class BookStoreHtmxDbSchemaMigrator : ITransientDependency
{
    private readonly IServiceProvider _serviceProvider;

    public BookStoreHtmxDbSchemaMigrator(
        IServiceProvider serviceProvider)
    {
        _serviceProvider = serviceProvider;
    }

    public async Task MigrateAsync()
    {
        
        /* We intentionally resolving the BookStoreHtmxDbContext
         * from IServiceProvider (instead of directly injecting it)
         * to properly get the connection string of the current tenant in the
         * current scope.
         */

        await _serviceProvider
            .GetRequiredService<BookStoreHtmxDbContext>()
            .Database
            .MigrateAsync();

    }
}
