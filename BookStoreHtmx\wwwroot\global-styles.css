:root {
    --lpx-theme-light-bg: url('/LeptonX/images/login-pages/login-bg-img-light.svg');
    --lpx-theme-dim-bg: url('/LeptonX/images/login-pages/login-bg-img-dim.svg');
    --lpx-theme-dark-bg: url('/LeptonX/images/login-pages/login-bg-img-dark.svg');
}

:root {
    --lpx-logo: url('/images/logo/leptonx/icon.svg');
    --lpx-logo-icon: url('/images/logo/leptonx/icon.svg');
}

/* HTMX Loading Indicators */
.htmx-indicator {
    display: none;
}

.htmx-request .htmx-indicator {
    display: inline;
}

.htmx-request.htmx-indicator {
    display: inline;
}

/* Loading spinner for HTMX requests */
.htmx-request {
    position: relative;
}

.htmx-request::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #3498db;
    border-radius: 50%;
    animation: htmx-spin 1s linear infinite;
    z-index: 1000;
}

@keyframes htmx-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Disable pointer events during HTMX requests */
.htmx-request {
    pointer-events: none;
    opacity: 0.6;
}

/* HTMX Table styles */
.htmx-table-container {
    position: relative;
}

.htmx-table-loading {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 100;
}

/* HTMX Modal styles */
.htmx-modal-loading {
    text-align: center;
    padding: 20px;
}

/* HTMX Form validation styles */
.htmx-form-errors {
    margin-top: 10px;
}

.htmx-form-errors .alert {
    margin-bottom: 5px;
}
