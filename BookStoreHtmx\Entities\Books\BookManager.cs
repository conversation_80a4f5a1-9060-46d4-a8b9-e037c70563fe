using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using JetBrains.Annotations;
using Volo.Abp;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Domain.Services;
using Volo.Abp.Data;

namespace BookStoreHtmx.Books
{
    public class BookManager : DomainService
    {
        protected IBookRepository _bookRepository;

        public BookManager(IBookRepository bookRepository)
        {
            _bookRepository = bookRepository;
        }

        public virtual async Task<Book> CreateAsync(
        Guid authorId, string name, DateOnly publishDate, float price, string? description = null)
        {
            Check.NotNull(authorId, nameof(authorId));
            Check.NotNullOrWhiteSpace(name, nameof(name));

            var book = new Book(
             GuidGenerator.Create(),
             authorId, name, publishDate, price, description
             );

            return await _bookRepository.InsertAsync(book);
        }

        public virtual async Task<Book> UpdateAsync(
            Guid id,
            Guid authorId, string name, DateOnly publishDate, float price, string? description = null, [CanBeNull] string? concurrencyStamp = null
        )
        {
            Check.NotNull(authorId, nameof(authorId));
            Check.NotNullOrWhiteSpace(name, nameof(name));

            var book = await _bookRepository.GetAsync(id);

            book.AuthorId = authorId;
            book.Name = name;
            book.PublishDate = publishDate;
            book.Price = price;
            book.Description = description;

            book.SetConcurrencyStampIfNotNull(concurrencyStamp);
            return await _bookRepository.UpdateAsync(book);
        }

    }
}