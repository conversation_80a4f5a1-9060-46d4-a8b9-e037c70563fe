using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Volo.Abp.Domain.Repositories;

namespace BookStoreHtmx.Authors
{
    public interface IAuthorRepository : IRepository<Author, Guid>
    {
        Task<List<Author>> GetListAsync(
            string? filterText = null,
            string? fullName = null,
            DateOnly? birthDateMin = null,
            DateOnly? birthDateMax = null,
            string? sorting = null,
            int maxResultCount = int.MaxValue,
            int skipCount = 0,
            CancellationToken cancellationToken = default
        );

        Task<long> GetCountAsync(
            string? filterText = null,
            string? fullName = null,
            DateOnly? birthDateMin = null,
            DateOnly? birthDateMax = null,
            CancellationToken cancellationToken = default);
    }
}