using System;
using System.Linq;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Entities.Auditing;
using Volo.Abp.MultiTenancy;
using JetBrains.Annotations;

using Volo.Abp;

namespace BookStoreHtmx.Authors
{
    public class Author : FullAuditedAggregateRoot<Guid>
    {
        [NotNull]
        public virtual string FullName { get; set; }

        public virtual DateOnly BirthDate { get; set; }

        [CanBeNull]
        public virtual string? Bio { get; set; }

        protected Author()
        {

        }

        public Author(Guid id, string fullName, DateOnly birthDate, string? bio = null)
        {

            Id = id;
            Check.NotNull(fullName, nameof(fullName));
            FullName = fullName;
            BirthDate = birthDate;
            Bio = bio;
        }

    }
}