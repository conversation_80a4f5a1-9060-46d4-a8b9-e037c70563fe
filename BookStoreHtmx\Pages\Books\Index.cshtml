@page
@using Microsoft.AspNetCore.Authorization
@using Volo.Abp.AspNetCore.Mvc.UI.Layout
@using BookStoreHtmx.Permissions
@using BookStoreHtmx.Web.Pages.Books
@using BookStoreHtmx.Menus
@using Microsoft.AspNetCore.Mvc.Localization
@using BookStoreHtmx.Localization
@inject IHtmlLocalizer<BookStoreHtmxResource> L
@inject IAuthorizationService Authorization
@model IndexModel
@inject IPageLayout PageLayout
@{
    PageLayout.Content.Title = L["Books"].Value;
    PageLayout.Content.MenuItemName = BookStoreHtmxMenus.Books;
}

@section styles
{

}

@section scripts
{
    <script>
        // HTMX-based Books page functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Handle advanced filter toggle
            document.getElementById('AdvancedFilterSectionToggler').addEventListener('click', function() {
                var section = document.getElementById('AdvancedFilterSection');
                var icon = this.querySelector('i');

                if (section.style.display === 'none') {
                    section.style.display = 'block';
                    icon.classList.remove('fa-angle-down');
                    icon.classList.add('fa-angle-up');
                } else {
                    section.style.display = 'none';
                    icon.classList.remove('fa-angle-up');
                    icon.classList.add('fa-angle-down');
                }
            });

            // Handle select all checkbox
            document.getElementById('select_all').addEventListener('change', function() {
                var checkboxes = document.querySelectorAll('.select-row-checkbox');
                checkboxes.forEach(function(checkbox) {
                    checkbox.checked = this.checked;
                }, this);
            });

            // Handle export to Excel
            document.getElementById('ExportToExcelButton').addEventListener('click', function(e) {
                e.preventDefault();

                // Get current filter values
                var formData = new FormData(document.getElementById('SearchForm'));
                var params = new URLSearchParams(formData);

                // Call the export service (you'll need to implement this)
                if (window.bookStoreHtmx && window.bookStoreHtmx.books && window.bookStoreHtmx.books.books) {
                    window.bookStoreHtmx.books.books.getDownloadToken().then(function(result) {
                        var url = abp.appPath + 'api/app/books/as-excel-file?' + params.toString() + '&downloadToken=' + result.token;
                        var downloadWindow = window.open(url, '_blank');
                        downloadWindow.focus();
                    });
                } else {
                    abp.notify.warn('Export functionality is not available.');
                }
            });
        });

        // Filter management functions
        function clearAuthorFilter() {
            document.getElementById('AuthorIdFilter').value = '';
            document.getElementById('Author_Filter_FullName').value = '';
            // Trigger search form submission to refresh results
            htmx.trigger(document.getElementById('SearchForm'), 'submit');
        }

        function clearAllFilters() {
            // Clear all filter inputs
            document.getElementById('FilterText').value = '';
            document.getElementById('NameFilter').value = '';
            document.getElementById('PublishDateFilterMin').value = '';
            document.getElementById('PublishDateFilterMax').value = '';
            document.getElementById('PriceFilterMin').value = '';
            document.getElementById('PriceFilterMax').value = '';
            document.getElementById('AuthorIdFilter').value = '';
            document.getElementById('Author_Filter_FullName').value = '';

            // Trigger search form submission to refresh results
            htmx.trigger(document.getElementById('SearchForm'), 'submit');
        }

        // Handle author selection from lookup modal
        function selectAuthor(authorId, authorName) {
            document.getElementById('AuthorIdFilter').value = authorId;
            document.getElementById('Author_Filter_FullName').value = authorName;

            // Close modal
            var modal = bootstrap.Modal.getInstance(document.getElementById('htmx-modal'));
            if (modal) {
                modal.hide();
            }

            // Trigger search form submission to refresh results
            htmx.trigger(document.getElementById('SearchForm'), 'submit');
        }

        // Handle HTMX events
        document.body.addEventListener('htmx:afterRequest', function(evt) {
            if (evt.detail.xhr.status === 200 && evt.detail.target.id === 'BooksTable') {
                // Refresh completed, update select all checkbox state
                document.getElementById('select_all').checked = false;
            }
        });
    </script>
}

@section content_toolbar {
    <abp-button id="ExportToExcelButton" text="@L["ExportToExcel"].Value" icon="download" size="Small" button-type="Primary" />
    @if (await Authorization.IsGrantedAsync(BookStoreHtmxPermissions.Books.Create))
    {
        <button class="btn btn-primary btn-sm"
                htmx-get="/Books/CreateModal"
                htmx-target="#htmx-modal-content"
                data-bs-toggle="modal"
                data-bs-target="#htmx-modal">
            <i class="fa fa-plus"></i> @L["NewBook"]
        </button>
    }

}



<abp-card>
    <abp-card-body>
		<abp-row class="mb-3">
            <abp-column size-md="_8" size-lg="_10">
                <div class="mb-3">
                    <form id="SearchForm" autocomplete="off"
                          htmx-get="/Books/TableData"
                          htmx-target="#BooksTable tbody"
                          htmx-trigger="submit, input delay:500ms from:#FilterText">
                        <div class="input-group">
                            <input class="form-control page-search-filter-text" id="FilterText" name="filterText" placeholder="@L["Search"]"/>
                            <abp-button button-type="Primary" type="submit" icon="search"/>
                        </div>
                    </form>
                </div>
            </abp-column>
            <abp-column size-md="_4" size-lg="_2">
                <div class="mb-3">
                    <abp-button style="width:100%" id="AdvancedFilterSectionToggler" button-type="Outline_Primary">
                        @L["Filters"]<i aria-hidden="true" class="fa ms-1 fa-angle-down"></i>
                    </abp-button>
                </div>
            </abp-column>
        </abp-row>

        <div id="AdvancedFilterSection" class="mt-3" style="display: none;">
            <div class="row">
                <div class="col-md-3">
                    <abp-input asp-for="NameFilter" label="@L["Name"].Value" />
                </div>
                <div class="col-md-3">
                    <abp-input type="date" asp-for="PublishDateFilterMin" label="@L["MinPublishDate"].Value" />
                </div>
                <div class="col-md-3">
                    <abp-input type="date" asp-for="PublishDateFilterMax" label="@L["MaxPublishDate"].Value" />
                </div>
                <div class="col-md-3">
                    <abp-input asp-for="PriceFilterMin" label="@L["MinPrice"].Value" />
                </div>
            </div>
            <div class="row">
                <div class="col-md-3">
                    <abp-input asp-for="PriceFilterMax" label="@L["MaxPrice"].Value" />
                </div>
                <div class="col-md-6">
                    <label for="Author_Filter_FullName">@L["Author"]</label>
                    <div class="input-group mb-3 mt-2">
                        <input hidden id="AuthorIdFilter" name="authorId" />
                        <input type="text" id="Author_Filter_FullName" class="form-control" disabled placeholder="@L["SelectAuthor"]">
                        <button type="button" class="btn btn-info text-light"
                                htmx-get="/Shared/LookupModal?serviceMethod=getAuthorLookup&currentId=&currentDisplayName="
                                htmx-target="#htmx-modal-content"
                                data-bs-toggle="modal"
                                data-bs-target="#htmx-modal">@L["Pick"]</button>
                        <button type="button" class="btn btn-danger ms-1"
                                onclick="clearAuthorFilter()">
                            <i class="fa fa-times"></i>
                        </button>
                    </div>
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <button type="button" class="btn btn-secondary me-2" onclick="clearAllFilters()">
                        <i class="fa fa-refresh"></i> @L["Clear"]
                    </button>
                    <button type="submit" class="btn btn-primary" form="SearchForm">
                        <i class="fa fa-search"></i> @L["Search"]
                    </button>
                </div>
            </div>
        </div>
        
        <div id="bulk-delete-context-menu" class="d-none">
            <div class="d-flex justify-content-between align-items-center mb-2">
                <p class="lead mb-0 d-none" id="items-selected-info-message"></p>
                            
                <div>
                    <button class="btn btn-outline-secondary d-none mx-1" id="select-all-items-btn"></button>
                                
                    <button class="btn btn-outline-secondary d-none mx-1" id="clear-selection-btn">
                        @L["ClearSelection"]
                    </button>
                                
                    <button class="btn btn-danger mx-1" id="delete-selected-items">
                        <i class="fa fa-trash"></i> @L["Delete"]
                    </button>
                </div>
                
            </div>
            
            <hr class="my-1 mx-0"/>
        </div>

        <div class="htmx-table-container">
            <table class="table table-striped" id="BooksTable">
                <thead>
                    <tr>
                        @if (await Authorization.IsGrantedAsync(BookStoreHtmxPermissions.Books.Delete))
                        {
                            <th><input type="checkbox" id="select_all" class="form-check-input" /></th>
                        }
                        <th>@L["Actions"]</th>
                        <th>@L["Name"]</th>
                        <th>@L["PublishDate"]</th>
                        <th>@L["Price"]</th>
                        <th>@L["Author"]</th>
                    </tr>
                </thead>
                <tbody htmx-get="/Books/TableData"
                       htmx-trigger="load, refresh from:body"
                       htmx-include="#SearchForm"
                       htmx-indicator="#table-loading">
                    <!-- Table rows will be loaded via HTMX -->
                </tbody>
            </table>
            <div id="table-loading" class="htmx-indicator text-center p-3">
                <div class="spinner-border" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
            </div>
        </div>
    </abp-card-body>
</abp-card>

<!-- HTMX Modal Container -->
<div class="modal fade" id="htmx-modal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content" id="htmx-modal-content">
            <!-- Modal content will be loaded via HTMX -->
        </div>
    </div>
</div>

