@page
@using Microsoft.AspNetCore.Authorization
@using Volo.Abp.AspNetCore.Mvc.UI.Layout
@using BookStoreHtmx.Permissions
@using BookStoreHtmx.Web.Pages.Books
@using BookStoreHtmx.Menus
@using Microsoft.AspNetCore.Mvc.Localization
@using BookStoreHtmx.Localization
@inject IHtmlLocalizer<BookStoreHtmxResource> L
@inject IAuthorizationService Authorization
@model IndexModel
@inject IPageLayout PageLayout
@{
    PageLayout.Content.Title = L["Books"].Value;
    PageLayout.Content.MenuItemName = BookStoreHtmxMenus.Books;
}

@section styles
{

}

@section scripts
{
    <abp-script src="/Pages/Books/index.js" />

}

@section content_toolbar {
    <abp-button id="ExportToExcelButton" text="@L["ExportToExcel"].Value" icon="download" size="Small" button-type="Primary" />
    @if (await Authorization.IsGrantedAsync(BookStoreHtmxPermissions.Books.Create))
    {
        <abp-button id="NewBookButton" text="@L["NewBook"].Value" icon="plus" size="Small" button-type="Primary" />
    }

}



<abp-card>
    <abp-card-body>
		<abp-row class="mb-3">
            <abp-column size-md="_8" size-lg="_10">
                <div class="mb-3">
                    <form id="SearchForm" autocomplete="off">
                        <div class="input-group">
                            <input class="form-control page-search-filter-text" id="FilterText" placeholder="@L["Search"]"/>
                            <abp-button button-type="Primary" type="submit" icon="search"/>
                        </div>
                    </form>
                </div>
            </abp-column>
            <abp-column size-md="_4" size-lg="_2">
                <div class="mb-3">
                    <abp-button style="width:100%" id="AdvancedFilterSectionToggler" button-type="Outline_Primary">
                        @L["Filters"]<i aria-hidden="true" class="fa ms-1 fa-angle-down"></i>
                    </abp-button>
                </div>
            </abp-column>
        </abp-row>

        <abp-row id="AdvancedFilterSection" class="mt-3" style="display: none;">
            <abp-column size="_3">
                <abp-input asp-for="NameFilter" label="@L["Name"].Value" />
            </abp-column>
            <abp-column size="_3">
                <abp-input type="date" asp-for="PublishDateFilterMin" label="@L["MinPublishDate"].Value" />
            </abp-column>

            <abp-column size="_3">
                <abp-input type="date" asp-for="PublishDateFilterMax" label="@L["MaxPublishDate"].Value" />
            </abp-column>

            <abp-column size="_3">
                <abp-input asp-for="PriceFilterMin" label="@L["MinPrice"].Value" />
            </abp-column>
            <abp-column size="_3">
                <abp-input asp-for="PriceFilterMax" label="@L["MaxPrice"].Value" />
            </abp-column>
                <abp-column size="_3">
                    <label for="Author_Filter_FullName">@L["Author"]</label>
                    <div class="input-group mb-3 mt-2">
                        <input hidden id="AuthorIdFilter" />
                        <input type="text" id="Author_Filter_FullName" class="form-control" disabled>
                        <abp-button button-type="Info" id="AuthorFilterLookupOpenButton" class="text-light">@L["Pick"]</abp-button>
                        <abp-button button-type="Danger" class="lookupCleanButton ms-1"><i class="fa fa-times"></i></abp-button>
                    </div>
                </abp-column>

        </abp-row>
        
        <div id="bulk-delete-context-menu" class="d-none">
            <div class="d-flex justify-content-between align-items-center mb-2">
                <p class="lead mb-0 d-none" id="items-selected-info-message"></p>
                            
                <div>
                    <button class="btn btn-outline-secondary d-none mx-1" id="select-all-items-btn"></button>
                                
                    <button class="btn btn-outline-secondary d-none mx-1" id="clear-selection-btn">
                        @L["ClearSelection"]
                    </button>
                                
                    <button class="btn btn-danger mx-1" id="delete-selected-items">
                        <i class="fa fa-trash"></i> @L["Delete"]
                    </button>
                </div>
                
            </div>
            
            <hr class="my-1 mx-0"/>
        </div>

        <abp-table striped-rows="true" id="BooksTable">
            <thead>
				<tr>
				    <th id="BulkDeleteCheckboxTheader"><input type="checkbox" id="select_all" class="form-check-input" /></th>
				    
					<th>@L["Actions"]</th>
					<th>@L["Name"]</th>
					<th>@L["PublishDate"]</th>
					<th>@L["Price"]</th>
					<th>@L["Author"]</th>

				</tr>
            </thead>
        </abp-table>
    </abp-card-body>
</abp-card>

