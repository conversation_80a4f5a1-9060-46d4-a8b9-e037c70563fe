using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Http;
using System.Text.Json;

namespace BookStoreHtmx.Extensions
{
    /// <summary>
    /// Extension methods for HTMX integration with ASP.NET Core and ABP Framework
    /// </summary>
    public static class HtmxExtensions
    {
        /// <summary>
        /// Checks if the request is an HTMX request
        /// </summary>
        public static bool IsHtmxRequest(this HttpRequest request)
        {
            return request.Headers.ContainsKey("HX-Request");
        }

        /// <summary>
        /// Checks if the request is an HTMX boosted request
        /// </summary>
        public static bool IsHtmxBoosted(this HttpRequest request)
        {
            return request.Headers.ContainsKey("HX-Boosted");
        }

        /// <summary>
        /// Gets the HTMX trigger element ID
        /// </summary>
        public static string GetHtmxTrigger(this HttpRequest request)
        {
            return request.Headers["HX-Trigger"].FirstOrDefault();
        }

        /// <summary>
        /// Gets the HTMX target element ID
        /// </summary>
        public static string GetHtmxTarget(this HttpRequest request)
        {
            return request.Headers["HX-Target"].FirstOrDefault();
        }

        /// <summary>
        /// Sets HTMX response headers for client-side actions
        /// </summary>
        public static IActionResult WithHtmxTrigger(this IActionResult result, string eventName, object eventData = null)
        {
            if (result is ViewResult viewResult)
            {
                var triggerData = eventData != null ? JsonSerializer.Serialize(eventData) : "true";
                viewResult.ViewData["HX-Trigger"] = $"{{\"{eventName}\": {triggerData}}}";
            }
            return result;
        }

        /// <summary>
        /// Sets HTMX redirect header
        /// </summary>
        public static IActionResult WithHtmxRedirect(this IActionResult result, string url)
        {
            if (result is ViewResult viewResult)
            {
                viewResult.ViewData["HX-Redirect"] = url;
            }
            return result;
        }

        /// <summary>
        /// Sets HTMX refresh header to refresh the page
        /// </summary>
        public static IActionResult WithHtmxRefresh(this IActionResult result)
        {
            if (result is ViewResult viewResult)
            {
                viewResult.ViewData["HX-Refresh"] = "true";
            }
            return result;
        }

        /// <summary>
        /// Creates a partial view result for HTMX requests, full view for regular requests
        /// </summary>
        public static IActionResult HtmxView(this Controller controller, string viewName = null, object model = null)
        {
            if (controller.Request.IsHtmxRequest())
            {
                return controller.PartialView(viewName, model);
            }
            return controller.View(viewName, model);
        }

        /// <summary>
        /// Creates a JSON result with HTMX-compatible success response
        /// </summary>
        public static IActionResult HtmxSuccess(this Controller controller, string message = null, object data = null)
        {
            var response = new
            {
                success = true,
                message = message,
                data = data
            };

            if (controller.Request.IsHtmxRequest())
            {
                // For HTMX requests, we can trigger client-side events
                controller.Response.Headers.Add("HX-Trigger", JsonSerializer.Serialize(new
                {
                    showNotification = new { type = "success", message = message }
                }));
            }

            return controller.Json(response);
        }

        /// <summary>
        /// Creates a JSON result with HTMX-compatible error response
        /// </summary>
        public static IActionResult HtmxError(this Controller controller, string message, object errors = null)
        {
            var response = new
            {
                success = false,
                message = message,
                errors = errors
            };

            if (controller.Request.IsHtmxRequest())
            {
                controller.Response.Headers.Add("HX-Trigger", JsonSerializer.Serialize(new
                {
                    showNotification = new { type = "error", message = message }
                }));
            }

            return controller.Json(response);
        }

        /// <summary>
        /// Closes modal and optionally refreshes target element
        /// </summary>
        public static IActionResult HtmxCloseModal(this Controller controller, string refreshTarget = null, string successMessage = null)
        {
            var triggerEvents = new Dictionary<string, object>
            {
                ["closeModal"] = true
            };

            if (!string.IsNullOrEmpty(refreshTarget))
            {
                triggerEvents["refreshTarget"] = refreshTarget;
            }

            if (!string.IsNullOrEmpty(successMessage))
            {
                triggerEvents["showNotification"] = new { type = "success", message = successMessage };
            }

            controller.Response.Headers.Add("HX-Trigger", JsonSerializer.Serialize(triggerEvents));

            return controller.Content("");
        }

        /// <summary>
        /// Returns appropriate response based on request type (HTMX vs regular)
        /// </summary>
        public static IActionResult HtmxPartialOrRedirect(this Controller controller, string partialViewName, string redirectAction, object model = null)
        {
            if (controller.Request.IsHtmxRequest())
            {
                return controller.PartialView(partialViewName, model);
            }
            return controller.RedirectToAction(redirectAction);
        }
    }

    /// <summary>
    /// HTMX response models for structured responses
    /// </summary>
    public class HtmxResponse
    {
        public bool Success { get; set; }
        public string Message { get; set; }
        public object Data { get; set; }
        public Dictionary<string, object> Errors { get; set; }
    }

    /// <summary>
    /// HTMX table response for paginated data
    /// </summary>
    public class HtmxTableResponse<T>
    {
        public IEnumerable<T> Data { get; set; }
        public int TotalCount { get; set; }
        public int PageIndex { get; set; }
        public int PageSize { get; set; }
        public bool HasNextPage => PageIndex * PageSize < TotalCount;
        public bool HasPreviousPage => PageIndex > 1;
    }
}
