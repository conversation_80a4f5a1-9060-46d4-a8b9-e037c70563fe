using Microsoft.Extensions.Localization;
using BookStoreHtmx.Localization;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Ui.Branding;

namespace BookStoreHtmx;

[Dependency(ReplaceServices = true)]
public class BookStoreHtmxBrandingProvider : DefaultBrandingProvider
{
    private IStringLocalizer<BookStoreHtmxResource> _localizer;

    public BookStoreHtmxBrandingProvider(IStringLocalizer<BookStoreHtmxResource> localizer)
    {
        _localizer = localizer;
    }

    public override string AppName => _localizer["AppName"];
}