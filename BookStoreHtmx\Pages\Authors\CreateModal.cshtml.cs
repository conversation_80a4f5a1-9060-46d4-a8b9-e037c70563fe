using Volo.Abp.AspNetCore.Mvc.UI.RazorPages;
using BookStoreHtmx.Shared;
using System;
using System.Collections.Generic;
using Microsoft.AspNetCore.Mvc.Rendering;
using Volo.Abp.Application.Dtos;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using BookStoreHtmx.Authors;
using BookStoreHtmx.Pages;
using BookStoreHtmx.Extensions;

namespace BookStoreHtmx.Web.Pages.Authors
{
    public class CreateModalModel : HtmxPageModelBase
    {

        [BindProperty]
        public AuthorCreateViewModel Author { get; set; }

        protected IAuthorsAppService _authorsAppService;

        public CreateModalModel(IAuthorsAppService authorsAppService)
        {
            _authorsAppService = authorsAppService;

            Author = new();
        }

        public virtual async Task OnGetAsync()
        {
            Author = new AuthorCreateViewModel();

            await Task.CompletedTask;
        }

        public virtual async Task<IActionResult> OnPostAsync()
        {
            return await ValidateAndProcessAsync(async () =>
            {
                await _authorsAppService.CreateAsync(ObjectMapper.Map<AuthorCreateViewModel, AuthorCreateDto>(Author));

                return HandleFormResult(
                    isSuccess: true,
                    successMessage: L["SuccessfullyCreated"],
                    redirectUrl: "./Index"
                );
            }, L["AnErrorOccurred"]);
        }
    }

    public class AuthorCreateViewModel : AuthorCreateDto
    {
    }
}